import json
import logging
import os
from typing import Any, Dict

from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.adk.tools import BaseTool
from google.adk.tools.tool_context import ToolContext

agent_log_level = 'info'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Create logs directory if it doesn't exist
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

import datetime

log_file_path = os.path.join(log_dir, f'agent_{datetime.date.today().strftime("%Y%m%d")}.log')

file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
file_handler.setLevel(logging.INFO)
# 获取ADK的Logger
logger = logging.getLogger("google_adk." + __name__)

# 移除ADK原有Handler
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
# 增大opentelemetry日志级别 临时解决方案
os.environ["OTEL_SDK_DISABLED"] = "true"
logging.getLogger("opentelemetry").setLevel(logging.CRITICAL)


def log_callback_context(callback_context: CallbackContext):
    """
    Logs the callback context information to a file.

    Args:
        callback_context (CallbackContext): The callback context to log.
    """
    if callback_context:
        log_to_file(f"Callback context:")
        log_to_file(f"  agent_name: {callback_context.agent_name}")
        log_to_file(f"  user_content: {callback_context.user_content}")
        log_to_file(f"  invocation_id: {callback_context.invocation_id}")
        if agent_log_level == 'debug':
            for name, value in callback_context.__dict__.items():
                log_to_file(f"  {name}: {value}")
            for name, value in callback_context.state.to_dict().items():
                log_to_file(f"  state.{name}: {value}")
    else:
        log_to_file("Callback context is None")


def lowercase_value(value):
    """Make dictionary lowercase"""
    if isinstance(value, dict):
        return {k: lowercase_value(v) for k, v in value.items()}
    elif isinstance(value, str):
        return value.lower()
    elif isinstance(value, (list, set, tuple)):
        tp = type(value)
        return tp(lowercase_value(i) for i in value)
    else:
        return value


def log_before_tool_invocation(tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext):
    """
    Logs the tool invocation information before a tool is invoked.

    Args:
        tool (BaseTool): The tool being invoked.
        args (Dict[str, Any]): The arguments passed to the tool.
        tool_context (CallbackContext): The callback context for the tool invocation.
    """
    # 统一成小写
    # args = lowercase_value(args)

    log_to_file(f"\n Before tool '{tool.name}' invocation: ======================== >>> \n")
    log_to_file(f"Tool: {tool.name}")
    log_to_file(f"Description: {tool.description}")
    # log_to_file(f"Args: {json.dumps(args, indent=2)}")
    log_to_file(f"Args: {args}")
    log_callback_context(tool_context)


def log_after_tool_invocation(tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext, tool_response: Dict):
    """
    Logs the tool invocation information after a tool is invoked.

    Args:
        tool (BaseTool): The tool that was invoked.
        args (Dict[str, Any]): The arguments passed to the tool.
        tool_context (ToolContext): The context of the tool invocation.
        tool_response (Dict): The response from the tool.
    """
    log_to_file(f"\n After tool '{tool.name}' invocation: ======================== >>> \n")
    log_to_file(f"Tool: {tool.name}")
    log_to_file(f"Description: {tool.description}")
    # log_to_file(f"Args: {json.dumps(args, indent=2)}")
    log_to_file(f"Args: {args}")
    log_callback_context(tool_context)
    log_to_file(f"Tool Response: {json.dumps(tool_response, indent=2)}")

def log_llm_request(llm_request: LlmRequest):
    """
    Logs the LLM request information.

    Args:
        llm_request (LlmRequest): The LLM request being made.
    """
    log_to_file("LLM Request:")
    if llm_request:
        try:
            log_to_file(f"  LLM Request: {llm_request.model_dump_json(indent=2)}")
        except Exception as e:
            log_to_file(f"  Error logging LLM Request: {str(e)}", level='error')
            log_to_file(f"  {llm_request}")
    else:
        log_to_file("  LLM Request is None")


def log_llm_response(llm_response: LlmResponse):
    """
    Logs the LLM response information.

    Args:
        llm_response (LlmResponse): The LLM response received.
    """
    log_to_file("LLM Response:")
    if llm_response:
        try:
            log_to_file(f"  LLM Response: {llm_response.model_dump_json(indent=2)}")
        except Exception as e:
            log_to_file(f"  Error logging LLM Response: {str(e)}", level='error')
            log_to_file(f"  {llm_response}")
    else:
        log_to_file("  LLM Response is None")


def log_before_model(callback_context: CallbackContext, llm_request: LlmRequest):
    """
    Logs the callback context before a model is invoked.

    Args:
        callback_context (CallbackContext): The callback context to log.
        llm_request (LlmRequest): The LLM request being made.
    """
    log_to_file("\n Before model callback: ======================== >>> \n")
    log_callback_context(callback_context)
    log_llm_request(llm_request)


import re

ask_format_rule_list = [
    {
        "agent_name": "travel_rec_agent",
        "rules": [
            {
                "traveling_days": {
                    "pattern": "(打算)?(玩|耍)(几天|多久) ",
                    "template": "您打算玩几天呢？"
                },
                "destination": {
                    "pattern": "(打算|想)?去(哪个|哪)(城市)? ",
                    "template": "您想去哪个城市呀？"
                },
                "source": {
                    "pattern": "(要)?从(哪|哪个)(城市)?(出发|去) ",
                    "template": "要从哪个城市出发呢？"
                }
            }
        ]
    }
]


def reg_match(text: str, agent_name: str, *args):
    print(f"agent ask reg match params: {text}-{agent_name}")
    if not text:
        return text
    for ask_format_rule in ask_format_rule_list:
        if ask_format_rule["agent_name"] == agent_name:
            for rule in ask_format_rule["rules"]:
                slots_key_list = rule.keys()
                for slots_key in slots_key_list:
                    pattern_template = rule.get(slots_key)
                    pattern = pattern_template["pattern"].strip()
                    if re.match(pattern, text):
                        return pattern_template["template"]
    return text


def log_after_model(callback_context: CallbackContext, llm_response: LlmResponse):
    """
    Logs the callback context after a model is invoked.

    Args:
        callback_context (CallbackContext): The callback context to log.
        llm_response (LlmResponse): The LLM response received.
    """
    log_to_file("\n After model callback: ======================== >>> \n")
    log_callback_context(callback_context)
    log_llm_response(llm_response)
    agent_name = callback_context.agent_name
    text = llm_response.content.parts[0].text
    result = reg_match(text, agent_name)
    llm_response.content.parts[0].text = result


def log_before_agent(callback_context: CallbackContext):
    log_to_file("\n Before agent callback: ======================== >>> \n")
    log_callback_context(callback_context)


def log_after_agent(callback_context: CallbackContext):
    log_to_file("\n After agent callback: ======================== >>> \n")
    log_callback_context(callback_context)


def log_to_file(message: str, level: str = 'info'):
    """
    Logs a message to a file.

    Args:
        message (str): The message to log.
        level (str): The log level (e.g., 'info', 'warning', 'error'). Defaults to 'info'.
    """
    if level == 'info':
        logger.info(message)
    elif level == 'warning':
        logger.warning(message)
    elif level == 'error':
        logger.error(message)
    elif level == 'debug':
        logger.debug(message)
    else:
        logger.info(message)  # Default to info if level is unknown
