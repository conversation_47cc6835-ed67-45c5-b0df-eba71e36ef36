"""
性能优化配置文件
包含各种性能相关的配置参数
"""

# 缓存配置
CACHE_CONFIG = {
    # 内存缓存TTL（秒）
    "MEMORY_CACHE_TTL": 300,  # 5分钟
    
    # Redis缓存TTL（秒）
    "REDIS_CACHE_TTL": 600,   # 10分钟
    
    # 最大缓存条目数
    "MAX_CACHE_ENTRIES": 1000,
    
    # LRU缓存大小
    "LRU_CACHE_SIZE": 100,
    
    # 缓存清理间隔（秒）
    "CACHE_CLEANUP_INTERVAL": 60
}

# 并发配置
CONCURRENCY_CONFIG = {
    # 最大并发任务数
    "MAX_CONCURRENT_TASKS": 10,
    
    # 线程池大小
    "THREAD_POOL_SIZE": 20,
    
    # 异步超时时间（秒）
    "ASYNC_TIMEOUT": 30,
    
    # 重试次数
    "MAX_RETRIES": 3,
    
    # 重试延迟（秒）
    "RETRY_DELAY": 1
}

# LLM调用优化配置
LLM_CONFIG = {
    # 批处理大小
    "BATCH_SIZE": 5,
    
    # 连接池大小
    "CONNECTION_POOL_SIZE": 10,
    
    # 请求超时（秒）
    "REQUEST_TIMEOUT": 30,
    
    # 最大令牌数
    "MAX_TOKENS": 4000,
    
    # 温度参数
    "TEMPERATURE": 0.7
}

# 性能监控配置
MONITORING_CONFIG = {
    # 是否启用性能监控
    "ENABLE_MONITORING": True,
    
    # 监控数据保留时间（秒）
    "MONITORING_RETENTION": 3600,  # 1小时
    
    # 性能报告间隔（秒）
    "REPORT_INTERVAL": 300,  # 5分钟
    
    # 慢查询阈值（秒）
    "SLOW_QUERY_THRESHOLD": 5.0
}

# 数据库连接池配置
DB_POOL_CONFIG = {
    # Redis连接池配置
    "REDIS_POOL": {
        "max_connections": 20,
        "retry_on_timeout": True,
        "health_check_interval": 30
    },
    
    # HTTP连接池配置
    "HTTP_POOL": {
        "pool_connections": 10,
        "pool_maxsize": 20,
        "max_retries": 3
    }
}

# 优化开关
OPTIMIZATION_FLAGS = {
    # 启用内存缓存
    "ENABLE_MEMORY_CACHE": True,
    
    # 启用并行处理
    "ENABLE_PARALLEL_PROCESSING": True,
    
    # 启用预加载
    "ENABLE_PRELOADING": True,
    
    # 启用压缩
    "ENABLE_COMPRESSION": False,
    
    # 启用批处理
    "ENABLE_BATCHING": True,
    
    # 启用连接复用
    "ENABLE_CONNECTION_REUSE": True
}

# 性能阈值配置
PERFORMANCE_THRESHOLDS = {
    # 规划生成最大时间（秒）
    "PLANNING_MAX_TIME": 30,
    
    # COT生成最大时间（秒）
    "COT_MAX_TIME": 10,
    
    # 景点搜索最大时间（秒）
    "ATTRACTION_MAX_TIME": 15,
    
    # 缓存命中率最低要求（%）
    "MIN_CACHE_HIT_RATE": 60,
    
    # 内存使用率警告阈值（%）
    "MEMORY_WARNING_THRESHOLD": 80
}

# 资源限制配置
RESOURCE_LIMITS = {
    # 最大内存使用（MB）
    "MAX_MEMORY_MB": 1024,
    
    # 最大CPU使用率（%）
    "MAX_CPU_PERCENT": 80,
    
    # 最大文件描述符数
    "MAX_FILE_DESCRIPTORS": 1000,
    
    # 最大网络连接数
    "MAX_NETWORK_CONNECTIONS": 100
}

def get_config(config_name: str) -> dict:
    """获取指定配置"""
    configs = {
        "cache": CACHE_CONFIG,
        "concurrency": CONCURRENCY_CONFIG,
        "llm": LLM_CONFIG,
        "monitoring": MONITORING_CONFIG,
        "db_pool": DB_POOL_CONFIG,
        "optimization": OPTIMIZATION_FLAGS,
        "thresholds": PERFORMANCE_THRESHOLDS,
        "limits": RESOURCE_LIMITS
    }
    return configs.get(config_name, {})

def is_optimization_enabled(flag_name: str) -> bool:
    """检查优化开关是否启用"""
    return OPTIMIZATION_FLAGS.get(flag_name, False)

def get_threshold(threshold_name: str) -> float:
    """获取性能阈值"""
    return PERFORMANCE_THRESHOLDS.get(threshold_name, 0)

# 动态配置更新
class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._configs = {
            "cache": CACHE_CONFIG.copy(),
            "concurrency": CONCURRENCY_CONFIG.copy(),
            "llm": LLM_CONFIG.copy(),
            "monitoring": MONITORING_CONFIG.copy(),
            "optimization": OPTIMIZATION_FLAGS.copy(),
            "thresholds": PERFORMANCE_THRESHOLDS.copy()
        }
    
    def update_config(self, config_name: str, updates: dict):
        """更新配置"""
        if config_name in self._configs:
            self._configs[config_name].update(updates)
    
    def get_config(self, config_name: str) -> dict:
        """获取配置"""
        return self._configs.get(config_name, {})
    
    def reset_config(self, config_name: str):
        """重置配置到默认值"""
        defaults = {
            "cache": CACHE_CONFIG,
            "concurrency": CONCURRENCY_CONFIG,
            "llm": LLM_CONFIG,
            "monitoring": MONITORING_CONFIG,
            "optimization": OPTIMIZATION_FLAGS,
            "thresholds": PERFORMANCE_THRESHOLDS
        }
        if config_name in defaults:
            self._configs[config_name] = defaults[config_name].copy()

# 全局配置管理器实例
config_manager = ConfigManager()
