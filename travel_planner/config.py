from google.adk.models.lite_llm import LiteLlm
from datetime import datetime, timedelta
from dotenv import load_dotenv
load_dotenv()

agent_app_name = "travel_planner"#与travel_planner目录保持一致

doubao_model = LiteLlm(
    model="volcengine/ep-20250306160115-4srlw",
    api_key='673d2aa3-5554-46dd-ab47-a0d5a48d6782',
    base_url='https://ark.cn-beijing.volces.com/api/v3',
    temperature=0.3
)

qwen_model = LiteLlm(
    model="openai/qwen-plus-latest",
    api_key='sk-ebd0ef97ddb34698b86466bb896657c9',
    base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
    temperature=0.3,
    # Add other parameters as needed, e.g., stop sequences
)

# gemini_model = LiteLlm(
#     model="vertex_ai/gemini-2.0-flash",
#     api_key='AIzaSyCUrvK4Qfgk3mEECLBiXCcNwtnpnHcmmHY',
#     base_url='https://generativelanguage.googleapis.com/v1beta/',
#     temperature=0.3,
#     # Add other parameters as needed, e.g., stop sequences
# )

gemini_model = "gemini-2.0-flash"


error_status_summary = "执行过程出现错误，请重试"

amap_key_list = [
    "5957ef1168d75dae34769afafb6cb8e2"
]