#!/usr/bin/env python3
"""
测试并行功能集成到 travel_rec_agent 的效果
"""

import sys
import os
import asyncio
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from travel_planner.subagents.travel_rec_agent import parallel_cot_attraction, cot, attraction


async def test_parallel_vs_sequential():
    """测试并行 vs 串行性能对比"""
    test_query = "北京出发，6月1日开始的为期3天的成都旅行计划，2个人，喜欢美食和文化景观"
    
    print("=== 测试并行功能集成 ===\n")
    
    # 测试并行执行
    print("1. 测试并行执行...")
    parallel_start = time.time()
    try:
        parallel_result = await parallel_cot_attraction(test_query)
        parallel_time = time.time() - parallel_start
        print(f"✅ 并行执行成功，耗时: {parallel_time:.2f}秒")
        print(f"返回状态: {parallel_result.get('status')}")
        print(f"描述长度: {len(parallel_result.get('desc', ''))}")
        
        # 检查工具结果
        tool_result = parallel_result.get('tool_result', {})
        print(f"COT结果: {'✅' if tool_result.get('cot') else '❌'}")
        print(f"景点结果: {'✅' if tool_result.get('attractions') else '❌'}")
        print(f"提示结果: {'✅' if tool_result.get('tips') else '❌'}")
        
    except Exception as e:
        print(f"❌ 并行执行失败: {e}")
        parallel_time = float('inf')
    
    print("\n" + "="*50 + "\n")
    
    # 测试串行执行（对比）
    print("2. 测试串行执行（对比）...")
    sequential_start = time.time()
    try:
        cot_result = await cot(test_query)
        attraction_result = await attraction(test_query)
        sequential_time = time.time() - sequential_start
        print(f"✅ 串行执行成功，耗时: {sequential_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ 串行执行失败: {e}")
        sequential_time = float('inf')
    
    print("\n" + "="*50 + "\n")
    
    # 性能对比
    print("3. 性能对比结果:")
    if parallel_time != float('inf') and sequential_time != float('inf'):
        improvement = ((sequential_time - parallel_time) / sequential_time) * 100
        print(f"并行执行时间: {parallel_time:.2f}秒")
        print(f"串行执行时间: {sequential_time:.2f}秒")
        print(f"性能提升: {improvement:.1f}%")
        
        if improvement > 0:
            print("🚀 并行执行更快！")
        else:
            print("⚠️ 并行执行较慢，可能需要优化")
    else:
        print("❌ 无法进行性能对比，存在执行失败")


async def test_error_handling():
    """测试错误处理和降级机制"""
    print("\n=== 测试错误处理 ===\n")
    
    # 测试空查询
    try:
        result = await parallel_cot_attraction("")
        print(f"空查询处理: {result.get('status')}")
    except Exception as e:
        print(f"空查询异常: {e}")
    
    # 测试异常查询
    try:
        result = await parallel_cot_attraction("这是一个无效的旅行查询测试")
        print(f"异常查询处理: {result.get('status')}")
    except Exception as e:
        print(f"异常查询异常: {e}")


if __name__ == "__main__":
    print("开始测试并行功能集成...")
    
    # 运行测试
    asyncio.run(test_parallel_vs_sequential())
    asyncio.run(test_error_handling())
    
    print("\n测试完成！")
