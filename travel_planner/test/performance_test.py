#!/usr/bin/env python3
"""
Travel Planner 性能测试脚本
测试优化前后的性能差异
"""

import time
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from travel_planner.tools.travel_planning_cot import (
    generate_cot,
    generate_attraction,
    generate_tip,
    generate_all_parallel
)
from travel_planner.tools.travel_planning import (
    doubao_planning,
    add_accommodations_and_attractions_info,
    update_budget,
    get_ticket_information
)


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.test_query = "北京出发，6月1日开始的为期3天的成都旅行计划，2个人，喜欢美食和文化景观"
        
    def test_original_sequential(self):
        """测试原始串行方法"""
        print("=== 测试原始串行方法 ===")
        start_time = time.time()
        
        # 串行执行COT、景点、提示
        cot_start = time.time()
        cot_result = generate_cot(self.test_query)
        cot_time = time.time() - cot_start
        print(f"COT生成耗时: {cot_time:.2f}秒")
        
        attraction_start = time.time()
        attraction_result = generate_attraction(self.test_query)
        attraction_time = time.time() - attraction_start
        print(f"景点生成耗时: {attraction_time:.2f}秒")
        
        tip_start = time.time()
        tip_result = generate_tip(self.test_query)
        tip_time = time.time() - tip_start
        print(f"提示生成耗时: {tip_time:.2f}秒")
        
        # 规划生成
        planning_start = time.time()
        planning_result = doubao_planning(self.test_query)
        planning_time = time.time() - planning_start
        print(f"规划生成耗时: {planning_time:.2f}秒")
        
        total_time = time.time() - start_time
        print(f"总耗时: {total_time:.2f}秒")
        
        return {
            "total_time": total_time,
            "cot_time": cot_time,
            "attraction_time": attraction_time,
            "tip_time": tip_time,
            "planning_time": planning_time,
            "results": {
                "cot": cot_result,
                "attraction": attraction_result,
                "tip": tip_result,
                "planning": planning_result
            }
        }
    
    async def test_optimized_parallel(self):
        """测试优化的并行方法"""
        print("\n=== 测试优化并行方法 ===")
        start_time = time.time()
        
        # 并行执行COT、景点、提示
        parallel_start = time.time()
        parallel_results = await generate_all_parallel(self.test_query)
        parallel_time = time.time() - parallel_start
        print(f"并行LLM调用耗时: {parallel_time:.2f}秒")
        
        # 规划生成（异步）
        planning_start = time.time()
        planning_result = await asyncio.to_thread(doubao_planning, self.test_query)
        planning_time = time.time() - planning_start
        print(f"规划生成耗时: {planning_time:.2f}秒")
        
        total_time = time.time() - start_time
        print(f"总耗时: {total_time:.2f}秒")
        
        return {
            "total_time": total_time,
            "parallel_time": parallel_time,
            "planning_time": planning_time,
            "results": {
                "parallel": parallel_results,
                "planning": planning_result
            }
        }
    
    async def test_planning_optimization(self):
        """测试规划后处理优化"""
        print("\n=== 测试规划后处理优化 ===")
        
        # 生成基础规划
        planning_result = await asyncio.to_thread(doubao_planning, self.test_query)
        if isinstance(planning_result, str):
            import json
            planning_result = json.loads(planning_result)
        
        # 测试串行后处理
        serial_start = time.time()
        enhanced_planning = add_accommodations_and_attractions_info(planning=planning_result)
        budget_planning = update_budget(planning=enhanced_planning)
        ticket_info = get_ticket_information(planning=budget_planning)
        serial_time = time.time() - serial_start
        print(f"串行后处理耗时: {serial_time:.2f}秒")
        
        # 测试并行后处理
        parallel_start = time.time()
        tasks = [
            asyncio.to_thread(add_accommodations_and_attractions_info, planning=planning_result),
            asyncio.to_thread(update_budget, planning=planning_result),
        ]
        enhanced_planning_p, budget_planning_p = await asyncio.gather(*tasks)
        
        # 合并结果
        final_planning = enhanced_planning_p
        if budget_planning_p and "budget" in budget_planning_p:
            final_planning["budget"] = budget_planning_p["budget"]
        
        # 获取票务信息
        ticket_info_p = await asyncio.to_thread(get_ticket_information, planning=final_planning)
        parallel_time = time.time() - parallel_start
        print(f"并行后处理耗时: {parallel_time:.2f}秒")
        
        improvement = ((serial_time - parallel_time) / serial_time) * 100
        print(f"性能提升: {improvement:.1f}%")
        
        return {
            "serial_time": serial_time,
            "parallel_time": parallel_time,
            "improvement": improvement
        }
    
    def run_comparison_test(self):
        """运行对比测试"""
        print("开始性能对比测试...")
        
        # 测试原始方法
        original_result = self.test_original_sequential()
        
        # 测试优化方法
        optimized_result = asyncio.run(self.test_optimized_parallel())
        
        # 测试后处理优化
        planning_result = asyncio.run(self.test_planning_optimization())
        
        # 计算性能提升
        total_improvement = ((original_result["total_time"] - optimized_result["total_time"]) 
                           / original_result["total_time"]) * 100
        
        print(f"\n=== 性能对比结果 ===")
        print(f"原始方法总耗时: {original_result['total_time']:.2f}秒")
        print(f"优化方法总耗时: {optimized_result['total_time']:.2f}秒")
        print(f"总体性能提升: {total_improvement:.1f}%")
        
        # LLM并行调用性能提升
        original_llm_time = (original_result["cot_time"] + 
                           original_result["attraction_time"] + 
                           original_result["tip_time"])
        llm_improvement = ((original_llm_time - optimized_result["parallel_time"]) 
                         / original_llm_time) * 100
        print(f"LLM调用性能提升: {llm_improvement:.1f}%")
        print(f"后处理性能提升: {planning_result['improvement']:.1f}%")
        
        return {
            "original": original_result,
            "optimized": optimized_result,
            "planning": planning_result,
            "total_improvement": total_improvement,
            "llm_improvement": llm_improvement
        }


def main():
    """主函数"""
    test = PerformanceTest()
    results = test.run_comparison_test()
    
    print(f"\n=== 优化建议 ===")
    if results["total_improvement"] > 20:
        print("✅ 性能优化效果显著！")
    elif results["total_improvement"] > 10:
        print("✅ 性能有明显提升")
    else:
        print("⚠️ 性能提升有限，可能需要进一步优化")
    
    print(f"💡 建议：")
    print(f"   - 继续优化API调用并发数")
    print(f"   - 考虑增加缓存机制")
    print(f"   - 优化数据处理流程")


if __name__ == "__main__":
    main()
