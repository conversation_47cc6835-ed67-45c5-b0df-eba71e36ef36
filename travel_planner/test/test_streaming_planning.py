#!/usr/bin/env python3
"""
测试流式 planning 工具的脚本
验证 AsyncGenerator 返回类型和流式输出功能
"""

import asyncio
import sys
import os
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from travel_planner.subagents.travel_rec_agent import planning
from google.adk.tools import ToolContext


class MockSession:
    """模拟会话对象"""
    def __init__(self, user_id="test_user"):
        self.user_id = user_id


class MockInvocationContext:
    """模拟调用上下文"""
    def __init__(self, user_id="test_user"):
        self.session = MockSession(user_id)


class MockToolContext:
    """模拟工具上下文"""
    def __init__(self, user_id="test_user"):
        self._invocation_context = MockInvocationContext(user_id)
        self.invocation_id = "test_invocation_123"


async def test_async_planning():
    """测试异步优化的 planning 工具"""
    print("🧪 开始测试异步优化的 planning 工具...")

    # 创建模拟的工具上下文
    tool_context = MockToolContext()

    # 测试参数
    test_params = {
        "query": "北京出发，成都3日游旅行攻略",
        "source": "北京",
        "destination": "成都",
        "travel_start_date": "2025-07-10",
        "traveling_days": 3,
        "type": "机票",
        "people": 2,
        "preference": "美食文化",
        "modify_content": "",
        "tool_context": tool_context
    }

    print(f"📋 测试参数: {test_params}")
    print("\n" + "="*50)
    print("🚀 开始异步执行:")
    print("="*50)

    try:
        # 调用异步 planning 工具
        result = await planning(**test_params)
        print(f"📦 收到结果:")
        print(f"   状态: {result.get('status', 'unknown')}")
        print(f"   描述长度: {len(result.get('desc', ''))}")
        print(f"   工具结果类型: {type(result.get('tool_result', 'N/A'))}")
        print(f"   插槽信息: {result.get('slots', {})}")
        print("-" * 30)

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n✅ 异步 planning 工具测试完成!")


async def test_error_handling():
    """测试错误处理"""
    print("\n🧪 开始测试错误处理...")

    tool_context = MockToolContext()

    # 测试无效参数
    invalid_params = {
        "query": "",
        "source": "",
        "destination": "",
        "travel_start_date": "invalid-date",
        "traveling_days": -1,
        "type": "invalid",
        "people": 0,
        "preference": "",
        "modify_content": "",
        "tool_context": tool_context
    }

    print("📋 测试无效参数的错误处理...")

    try:
        result = await planning(**invalid_params)
        print(f"📦 错误处理结果: {result.get('status')} - {result.get('desc')}")
        if result.get('status') == 'final':
            print("✅ 错误处理正常")
    except Exception as e:
        print(f"⚠️ 错误处理测试异常: {str(e)}")


def test_return_type():
    """测试返回类型注解"""
    print("\n🧪 测试返回类型注解...")

    import inspect
    from typing import get_type_hints

    # 获取函数签名
    sig = inspect.signature(planning)
    print(f"📋 函数签名: {sig}")

    # 获取类型提示
    try:
        hints = get_type_hints(planning)
        return_type = hints.get('return')
        print(f"📋 返回类型注解: {return_type}")

        # 检查是否是 Dict
        if hasattr(return_type, '__origin__'):
            print(f"📋 类型原型: {return_type.__origin__}")
            if hasattr(return_type, '__args__'):
                print(f"📋 类型参数: {return_type.__args__}")

        print("✅ 返回类型注解检查完成")

    except Exception as e:
        print(f"⚠️ 类型注解检查异常: {str(e)}")


async def main():
    """主测试函数"""
    print("🎯 开始 travel_rec_agent planning 工具异步优化测试")
    print("=" * 60)

    # 测试返回类型
    test_return_type()

    # 测试异步优化
    await test_async_planning()

    # 测试错误处理
    await test_error_handling()

    print("\n" + "=" * 60)
    print("🎉 所有测试完成!")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
