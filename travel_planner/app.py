import httpx
import argparse
import uvicorn
import traceback
import asyncio
import json
import redis_utils
from fastapi import (
    FastAPI,
    HTTPException,
    WebSocket,
    WebSocketDisconnect
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, <PERSON>
from typing import (
    Dict,
    List
)
from entity import (
    AppRequest,
    AgentPayload,
    NewMessage,
    Part,
    StatusCode
)
from config import agent_app_name
from utils import (
    data_format,
    build_error_chunk,
    build_heartbeat_chunk,
    location_to_city,
    context_manager,
    save_chunk_to_memory,
    save_chunk_to_dialog_context
)
from uuid import uuid4

class ConnectionManager:
    def __init__(self):
        # { request_id: WebSocket }
        self.active: Dict[str, WebSocket] = {}

    async def connect(self, request_id: str, ws: WebSocket):
        self.active[request_id] = ws

    def disconnect(self, request_id: str):
        self.active.pop(request_id, None)

    async def send_to(self, request_id: str, message: dict):
        ws = self.active.get(request_id)
        if ws:
            print("Logging message: request-id: {}\nmessage:{}".format(
                request_id,
                message
            ))
            await ws.send_json(message)

manager = ConnectionManager()
app = FastAPI()
# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get('/health')
def health_check():
    return {"status": "ok"}


# 全局端口变量
ADK_PORT = 8000
APP_PORT = 11513
# TODO: 将session_map持久化存储
session_map = set()
session_user_location_map = dict()

"""
    流式WebSocket: chat, 实时双向通信
    request:
        {
            "user_id": "u_0521_2th",
            "session_id": "s_0521_2th",
            "text": "帮我订张机票"
        }
    response:
        {
            "code": code,
            "summary_info": summary_info,
            "data": data
        }
"""

@app.websocket("/chat")
async def websocket_chat(websocket: WebSocket):
    await websocket.accept()
    stop_heartbeat = asyncio.Event()
    heartbeat_task = None
    request_id: str = None  # 用于在全局范围内存储当前连接的request_id

    async def heartbeat():
        try:
            while not stop_heartbeat.is_set():
                # send a simple heartbeat frame; adjust as needed
                await manager.send_to(request_id, build_heartbeat_chunk())
                await asyncio.sleep(5)
        except (WebSocketDisconnect, asyncio.CancelledError):
            pass
    
    try:
        data = await websocket.receive_json()
        try:
            req = AppRequest(**data)
        except Exception as e:
            await websocket.send_json(build_error_chunk(error_desc=str(e)))
            await websocket.close()
            return
        req.session_id=str(uuid4())
        print("Logging request: {}".format(req))
        session_key = f"{req.user_id}:{req.session_id}"
        session_url =  f"http://localhost:{ADK_PORT}/apps/{agent_app_name}/users/{req.user_id}/sessions/{req.session_id}"
        run_sse_url = f"http://localhost:{ADK_PORT}/run_sse"
        # 构造 request_id，并注册到 ConnectionManager
        request_id = session_key
        await manager.connect(request_id, websocket)

        async with httpx.AsyncClient(timeout=None, trust_env=True) as client:
            # 创建会话（如果未缓存）
            if session_key not in session_map:
                resp = await client.post(session_url)
                if resp.status_code not in (200, 201):
                    await manager.send_to(request_id, build_error_chunk(error_desc="Failed to create session"))
                    await websocket.close()
                    return
                session_map.add(session_key)

            # 获取location->city
            if session_key not in session_user_location_map:
                city = location_to_city(
                    lat=req.lat,
                    lng=req.lng,
                    location_system=req.location_system
                )
                if city:
                    session_user_location_map[session_key] = city

            # 构造对话上下文
            # 1. 获取userId级别context
            # 2. 获取业务参数，例如location等
            # 3. 拼接上下文
            user_id=req.user_id
            dialog_context_list = redis_utils.query_memory(f"multi_agents:travel_planner:memory:dialogContext:{user_id}")
            print(f"{user_id} get dialog context: {dialog_context_list}")
            if  dialog_context_list:
                dialog_context_list = dialog_context_list[::-1]
            text = context_manager(
                query=req.text,
                histories=dialog_context_list,
                location=session_user_location_map.get(session_key, "未知")
            )
            print("{}: this request total context:\n {}".format(user_id, text))

            payload = AgentPayload(
                app_name=agent_app_name,
                user_id=req.user_id,
                session_id=req.session_id,
                new_message=NewMessage(
                    role="user",
                    parts=[Part(text=text)]),
                streaming=False
            )

            # 发起 SSE 请求并实时转发到 WebSocket
            headers = {"Accept": "text/event-stream"}
            # 定义早停状态码
            EarlyStopCodes = [
                StatusCode.EndChunkPlaneTicketURL.name,
                StatusCode.EndChunkTrainTicketURL.name,
                StatusCode.EndChunkHotelJson.name, 
                StatusCode.EndChunkTravelJson.name,
                StatusCode.EndChunkOrderURL.name,
                StatusCode.Error.name
            ]
            # 定义需要send的状态码
            NeedSendCodes = [
                StatusCode.HeartBeat.name,
                StatusCode.ThinkingCOT.name,
                StatusCode.ThinkingCOTEnd.name,
                StatusCode.Error.name,
                StatusCode.Unknown.name,
                StatusCode.Text.name,
                StatusCode.EndChunk.name,
                StatusCode.EndChunkPlaneTicketURL.name,
                StatusCode.EndChunkTrainTicketURL.name,
                StatusCode.EndChunkHotelJson.name, 
                StatusCode.EndChunkTravelJson.name,
                StatusCode.EndChunkOrderURL.name,
                # StatusCode.FunctionCall.name,
                # StatusCode.FunctionResponse.name
            ]
            
            async with client.stream(
                "POST",
                run_sse_url,
                headers=headers,
                json=payload.model_dump()
            ) as resp:
                # sse方式迭代resp时，start heartbeat task
                heartbeat_task = asyncio.create_task(heartbeat())

                if resp.status_code != 200:
                    await manager.send_to(request_id, build_error_chunk(error_desc="Failed to initiate SSE conversation"))
                    await websocket.close()
                    return

                # 持续迭代每一行推送
                async for line in resp.aiter_lines():
                    if line and line.startswith("data: "):
                        line = line.replace("data: ", "").strip()
                        line_json = json.loads(line)
                        chunk_data_list = data_format(data=line_json)
                        for chunk_data in chunk_data_list:
                            chunk_status_code = chunk_data.get("code", None)
                            if chunk_status_code not in NeedSendCodes:
                                continue
                            # 将任务执行结果save至memory
                            if chunk_status_code in [
                                StatusCode.EndChunkTravelJson.name,
                                StatusCode.EndChunkHotelJson.name,
                                StatusCode.EndChunkTrainTicketURL.name,
                                StatusCode.EndChunkPlaneTicketURL.name
                            ]:
                                save_chunk_to_memory(
                                    user_id=req.user_id,
                                    query=req.text,
                                    chunk_data=chunk_data,
                                    chunk_status_code=chunk_status_code
                                )
                            # 存储对话上下文
                            if chunk_status_code in [StatusCode.EndChunk.name, StatusCode.Text.name]:
                                save_chunk_to_dialog_context(
                                    user_id=req.user_id,
                                    query=req.text,
                                    chunk_data=chunk_data,
                                    chunk_status_code=chunk_status_code
                                )

                            if chunk_status_code == StatusCode.ThinkingCOT.name:
                                await asyncio.sleep(0.5)
                            await manager.send_to(request_id, chunk_data)
                            # 提前结束，不需等待子Agent把结果传递给RootAgent生成回复帧
                            if chunk_status_code in EarlyStopCodes:
                                await resp.aclose()
                                await websocket.close()
                                return
        
    except WebSocketDisconnect:
        pass
    except Exception as e:
        traceback.print_exc()
        if request_id:
            await manager.send_to(request_id, build_error_chunk(error_desc=str(e)))
    finally:
        """
            anyway:
            1. stop heartbeat loop and cancel task
            2. close sse
            3. close websocket
            4. delete from manager
        """
        stop_heartbeat.set()
        if heartbeat_task is not None:
            heartbeat_task.cancel()
        try:
            if heartbeat_task is not None:
                await heartbeat_task
            try:
                await resp.aclose()
            except:
                pass
            try:
                await websocket.close()
            except:
                pass
        except:
            pass
        if request_id:
            manager.disconnect(request_id)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='WebSocket Chat Service')
    parser.add_argument('--adk-port', type=int, default=8000, 
                        help='Port for ADK service (default: 8000)')
    parser.add_argument('--app-port', type=int, default=11513,
                        help='Port for this WebSocket app (default: 11513)')
    
    args = parser.parse_args()
    
    ADK_PORT = args.adk_port
    APP_PORT = args.app_port
    print("adk port: {}, app port: {}".format(ADK_PORT, APP_PORT))
    
    uvicorn.run(app, host="0.0.0.0", port=APP_PORT)

