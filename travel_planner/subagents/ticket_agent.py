import os
import traceback
import asyncio
from google.adk import Agent
from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from travel_planner.config import (
    doubao_model,
    qwen_model,
    gemini_model
)
from travel_planner.tools.iata import read_iata_file
from travel_planner.tools.ticket_search import search_wrapper
from travel_planner.tools.flignt_search import search_flights_wrapper
from travel_planner.tools.get_date import (
    today,
    tomorrow
)
from google.adk.tools import ToolContext

from travel_planner.agent_loggers import log_to_file
from travel_planner.tools.memory import match_latest_slot_value
from travel_planner.agent_loggers import (log_before_agent, log_after_agent,
                                          log_before_model, log_after_model,
                                          log_before_tool_invocation, log_after_tool_invocation,
                                          )

"""
产品预定话术：
    为您准备了三种出行方案：
    💸 价格最优 ｜⏱️ 用时最短 ｜🌅 出发最早
    点开卡片看看哪种最适合你吧～

    {
        "cheapest_price": [],
        "shortest_totaltime": [],
        "earliest_starttime": []
    }
"""


def generate_desc(
        date: str,
        source: str,
        destination: str,
        type: str,
        search_result: dict
):
    valid_rec_nums = 0
    valid_rec_desc = []
    rec_type_dict = {
        "cheapest_price": "💸 价格最优",
        "shortest_totaltime": "⏱️ 用时最短",
        "earliest_starttime": "🌅 出发最早"
    }
    for key, value in search_result.items():
        if len(value) > 0:
            valid_rec_nums += 1
            valid_rec_desc.append(rec_type_dict[key])

    if valid_rec_nums == 0:
        desc = """{} {}到{}的{}，没有找到合适的票务信息""".format(date, source, destination, type)
    else:
        desc = """{} {}到{}的{}，为您准备了{}种出行方案：
        {}
        点开卡片看看哪种最适合你吧～""".format(date, source, destination, type, valid_rec_nums, "|".join(valid_rec_desc))

    return desc


async def book_plane_ticket(
    source: str = "",
    destination: str = "",
    date: str = tomorrow(),
    type: str = "",
    tool_context: ToolContext = None,
    *args,
    **kwargs
):
    try:
        if source.endswith("市"):
            source = source[:-1]
        if destination.endswith("市"):
            destination = destination[:-1]

        _, city2iata = read_iata_file(r"./travel_planner/tools/iata.txt")
        source_iata, dest_iata = city2iata[source], city2iata[destination]
        # 携程h5链接
        h5_url = r"https://m.ctrip.com/html5/flight/taro/first?from=inner&tripType=ONE_WAY&dcityName={}&acityName={}&ddate={}&isNew=true&dcity={}&acity={}".format(
            source,
            destination,
            date,
            source_iata,
            dest_iata
        )
        # 先查询redis
        session = tool_context._invocation_context.session
        invocation_id = tool_context.invocation_id
        user_id = session.user_id
        slots = {
            "source": source,
            "destination": destination,
            "date": date,
        }
        extract_result_from_redis = match_latest_slot_value(user_id=user_id,
                                                            chunk_code="EndChunkPlaneTicketURL", slots=slots)
        if extract_result_from_redis:
            log_to_file(f"find book plane ticket from cache and invocation_id:{invocation_id}", level="info")
            return extract_result_from_redis

        search_result = await asyncio.to_thread(
            search_flights_wrapper,
            start_city=source,
            end_city=destination,
            date=date
        )

        desc = generate_desc(
            date,
            source,
            destination,
            type,
            search_result
        )

        return {
            "status": "final",
            "desc": desc,
            "tool_result": {
                "source": source,
                "destination": destination,
                "date": date,
                "url": h5_url,
                "details": search_result
            },
            "slots": {
                "source": source,
                "destination": destination,
                "date": date
            }
        }
    except:
        traceback.print_exc()
        return {}


async def book_train_ticket(
    source: str = "",
    destination: str = "",
    date: str = tomorrow(),
    type: str = "",
    tool_context: ToolContext = None,
    *args,
    **kwargs
):
    try:
        if source.endswith("市"):
            source = source[:-1]
        if destination.endswith("市"):
            destination = destination[:-1]
        h5_url = r"https://m.ctrip.com/webapp/train/list?allianceid=&sid=&ticketType=0&dStation={}&aStation={}&dDate={}".format(
            source, destination, date)
        # 先查询redis
        session = tool_context._invocation_context.session
        invocation_id = tool_context.invocation_id
        user_id = session.user_id
        slots = {
            "source": source,
            "destination": destination,
            "date": date,
        }
        extract_result_from_redis = match_latest_slot_value(user_id=user_id,
                                                            chunk_code="EndChunkTrainTicketURL", slots=slots)
        if extract_result_from_redis:
            log_to_file(f"book train ticket match from cache and invocation_id:{invocation_id}", level="info")
            return extract_result_from_redis

        search_result = await asyncio.to_thread(
            search_wrapper,
            start_city=source,
            end_city=destination,
            date=date
        )

        desc = generate_desc(
            date,
            source,
            destination,
            type,
            search_result
        )

        return {
            "status": "final",
            "desc": desc,
            "tool_result": {
                "source": source,
                "destination": destination,
                "date": date,
                "url": h5_url,
                "details": search_result
            },
            "slots": slots
        }
    except:
        traceback.print_exc()
        return {}

model = doubao_model
instruction = """你是一个优秀的AI助手，熟练掌握thinking step by step方式，你的任务是帮用户进行飞机票和火车票票务查询。
飞机票和火车票票务预定均有四个参数：
    -- source（出发地，必填），出发地获取严格按照下面顺序：
        -- 当用户明确表达“帮我订个票 从X到Y”，直接取X作为出发地。例如，帮我订个票 北京飞上海，source=北京。
        -- 当用户未明确表达，且用户当前城市不是未知，而是北京、上海、成都等明确的城市名称时，取用户当前城市为出发地。例如，用户表述帮我订个票/帮我订个去沈阳的票，上下文知用户当前城市为天津，source=天津。
        -- 若用户未明确表达，且用户当前城市未知，则主动询问用户：“要从哪个城市出发？”。例如，用户表述帮我订个票/帮我订个去沈阳的票，上下文知用户当前城市未知，发起多轮交互询问用户“要从哪里出发？”
    -- destination（目的地，必填）：未指定时询问“您想去哪个城市？”
    -- date（日期，可选，格式 yyyy‑mm‑dd）：默认为“明天”；未指定时使用默认，无需额外询问。
    -- type（出行方式，必填，枚举值，取值“机票”或“火车票”）：
        -- 若用户明确表述“帮我订个票 X飞到Y”或“帮我订个飞机票”，则type=机票
        -- 若用户明确表述“帮我订个票 X坐到Y”或“帮我订个火车票”，则type=火车票
        -- 若用户未明确表述出行方式，发起多轮交互询问“您想坐飞机还是火车？”

请注意⚠️：
1. 确保先调用“today”工具获取今天日期，格式为yyyy-mm-dd。
2. 当无法判断“出发地”、“目的地”和“出行方式”时，进行多轮对话，询问用户。
询问必填参数时：
       -- 绝对禁止生成任何解释、推理或说明文字
2. 当用户上下文语义完整（包含必填参数）时，且用户表达预定机票，调用“book_plane_ticket”工具执行飞机票预定。
3. 当用户上下文语义完整（包含必填参数）时，且用户表达预定火车票、高铁票，调用“book_train_ticket”工具执行火车票预定。
"""

ticket_agent = LlmAgent(
    name="ticket_agent",
    model=model,
    description="订火车票、订机票",
    instruction=instruction,
    tools=[today, book_plane_ticket, book_train_ticket],
    before_agent_callback=[log_before_agent],
    after_agent_callback=[log_after_agent],
    before_model_callback=[log_before_model],
    after_model_callback=[log_after_model],
    before_tool_callback=[log_before_tool_invocation],
    after_tool_callback=[log_after_tool_invocation],
)