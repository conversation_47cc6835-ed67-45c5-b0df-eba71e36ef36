import os
import random
import json
import asyncio
import traceback
from typing import AsyncGenerator, Dict, Any
from google.adk import Agent
from google.adk.agents import LlmAgent
from google.adk.tools import ToolContext
from google.adk.models.lite_llm import LiteLlm
from travel_planner.agent_loggers import log_to_file
from travel_planner.tools.memory import match_latest_slot_value
from travel_planner.config import (
    doubao_model,
    qwen_model,
    gemini_model
)
from travel_planner.tools.get_date import (
    today,
    tomorrow
)
from travel_planner.tools.travel_planning import (
    doubao_planning,
    add_accommodations_and_attractions_info,
    update_budget,
    get_ticket_information,
    generate_markdown
)
from travel_planner.tools.travel_planning_cot import (
    generate_cot,
    generate_attraction,
    generate_tip
)
from travel_planner.agent_loggers import (log_before_agent, log_after_agent,
                                          log_before_model, log_after_model,
                                          log_before_tool_invocation, log_after_tool_invocation,
                                          )
from travel_planner.tools.slot_utils import date_convert_2025

"""
    由于generate_cot等方法存在默认参数,即prompt
    直接让agent调用generate_cot等方法时,llm会伪造一个prompt参数值导致执行不符合预期
    因此,对上述方法再次进行封装
"""


async def cot(query: str):
    cot_output = await asyncio.to_thread(generate_cot, query=query)

    return {
        "status": "final",
        "desc": "{}".format("\n\n{}".format(cot_output)),
        "tool_result": cot_output
    }

def generate_cot_end_desc():
    emotion_list = [
        "🎉",
        "😃",
        "😁",
        "😄",
        "😊",
        "🥳"
    ]

    cot_end_desc_list = [
        """本次行程规划将从多个维度为您保驾护航：\n* 首先锁定高性价比的交通方案，再精选多种风格的住宿选项，满足不同需求；\n* 随后深入挖掘当地热门景点，科学安排每日行程，确保游览丰富且高效；\n* 同时精准预算各项开支，帮助您合理控制费用；\n* 并附上贴心旅行提醒，规避潜在风险；\n* 最终将所有内容有机整合，呈现一份内容详实、排版精美的完整旅行报告，便于您随时查阅与参考。""",
        """本次旅行规划将以系统化流程逐步推进：\n* 首先完成出行票务的精准预定，确保交通衔接顺畅；\n* 继而依据目的地特色与你的偏好推荐多元住宿方案，从经济民宿到品质酒店全覆盖；\n* 同步展开景点深度搜索，挖掘地标景观与小众秘境；\n* 随后以时间轴为脉络制定行程安排，兼顾游玩节奏与体验深度；\n* 同步进行预算估算，分项罗列交通、住宿等开支；\n* 附上旅行必备提醒，涵盖天气预警、文化禁忌等实用信息。\n* 最终将各环节内容汇编成逻辑清晰、版式精美、易于查阅的完整报告，让行程规划一目了然。""",
        """正在给您带来一份详尽且实用的旅行指南，我们正全力整合出行票务、住宿推荐、景点攻略、行程规划等海量信息，精心打磨完整旅行报告。\n* 目前系统正高速运转，对交通时刻、酒店评分、景点开放时间等数据进行交叉核验，同时结合当地天气、节庆活动等动态信息优化行程逻辑。\n* 由于报告涵盖吃住行游购娱全维度内容，每一项数据都需反复校准，排版设计也在同步进行精细化调整，确保图文并茂且重点突出。\n* 您的耐心等待非常值得，稍后将为您呈现一份兼具实用性与美观度的旅行报告，助您轻松解锁旅途的每一个精彩瞬间～""",
        """为了给您奉上一份详尽实用的旅行指南，我们正全力整合出行票务、精选住宿、景点攻略、行程规划等​多维信息​，精心打磨完整旅行报告。\n* 当前，系统正高效运行，对交通时刻、酒店评分、景点开放时间等关键数据进行​实时核验，并结合当地天气、节庆活动等动态信息优化行程安排。\n* 由于报告​全面覆盖“吃住行游购娱”六大旅行维度​，每一项数据都经过​反复校准，排版设计也在进行​精细化调整​，力求​图文并茂、重点突出。\n* 您的耐心等待​定有所值，稍后我们将为您呈现这份兼具实用性与美观度的旅行报告，助您轻松解锁旅途的​每一刻精彩​！""",
        """我们正在全力打造一份兼具深度与美感的旅行指南，全面整合机票与车次信息、优质酒店推荐、景点开放详情和行程安排等多维数据。\n* 系统正在实时校对交通时刻、酒店评分与门票情况，并结合当地天气及节庆活动动态，持续优化您的行程。\n* 报告涵盖“吃、住、行、游、购、娱”六大板块，排版设计精细考究，图文并茂，层次分明。\n* 请稍作等待，稍后将为您奉上这份既实用又赏心悦目的旅行宝典，助您开启无忧之旅！""",
        """我们正在全力打造一份兼具深度与可操作性的旅行指南：\n* 系统已在后台同步汇总机票和车次信息、精选酒店推荐、核心景点数据和行程方案，并对交通时刻、酒店评分、景点等进行多重验证。\n* 同时，结合实时天气与当地节庆，动态优化行程安排。报告全面覆盖“吃、住、行、游、购、娱”六大维度，排版精细考究，图文并茂、重点突出。\n* 请稍作等待，一份既实用又赏心悦目的完整旅行报告即将呈现，助您尽享旅途每一刻精彩！"""
    ]

    return [
        "\n\n### **旅行报告生成中**",
        "",
        "{}{}".format(
            random.choice(emotion_list),
            random.choice(cot_end_desc_list)
        )
    ]


# 景点
async def attraction(query: str):
    attraction_output = await asyncio.to_thread(generate_attraction, query=query)

    def generate_markdown():
        lines = []
        lines.append("\n\n### **景点搜索**\n")
        description = attraction_output.get("description", "")
        lines.append(f"{description}。\n")

        for attr in attraction_output.get("attractions", []):
            name = attr.get("name", "")
            desc = attr.get("description", "")
            lines.append(f"* {name}：**{desc}**")

        lines.extend(generate_cot_end_desc())
        return "\n".join(lines)

    return {
        "status": "final",
        "desc": "{}".format(generate_markdown()),
        "tool_result": attraction_output
    }


# 旅行贴士
async def tips(query: str):
    tips_output = await asyncio.to_thread(generate_tip, query=query)

    def generate_markdown():
        tips = tips_output.get("tips", [])
        md_lines = ["\n\n### **旅行小贴士**", ""]
        for tip in tips:
            md_lines.append(f"* {tip}")

        md_lines.extend(generate_cot_end_desc())
        return "\n".join(md_lines)

    return {
        "status": "final",
        "desc": "{}".format(generate_markdown()),
        "tool_result": tips_output
    }

async def planning(
    query: str,
    source: str,
    destination: str,
    travel_start_date: str,
    traveling_days: int,
    type: str,
    people: int,
    preference: str,
    modify_content: str,
    tool_context: ToolContext,
    *args,
    **kwargs
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    流式旅行规划工具，支持中间结果的实时返回

    Args:
        query: 用户查询
        source: 出发地
        destination: 目的地
        travel_start_date: 出发日期
        traveling_days: 旅行天数
        type: 交通类型
        people: 人数
        preference: 偏好
        modify_content: 修改内容
        tool_context: 工具上下文

    Yields:
        Dict[str, Any]: 包含状态、描述、进度等信息的字典
    """
    try:
        travel_start_date = date_convert_2025(input_date=travel_start_date)
        log_to_file(f"planning params: {query}\n-{source}-{destination}-{travel_start_date}-{traveling_days}-{type}-{people}-{preference}-{modify_content}")

        slots = {
            "source": source,
            "destination": destination,
            "travel_start_date": travel_start_date,
            "traveling_days": traveling_days,
            "type": type,
            "people": people,
            "preference": preference,
            "modify_content": modify_content
        }

        # 获取会话信息
        session = tool_context._invocation_context.session
        invocation_id = tool_context.invocation_id
        user_id = session.user_id

        # 先查询redis缓存
        extract_result_from_redis = match_latest_slot_value(
            user_id=user_id,
            chunk_code="EndChunkTravelJson",
            slots=slots
        )

        log_to_file(f"planning match result from redis:{extract_result_from_redis}", level="info")
        if extract_result_from_redis:
            log_to_file(f"planning match from cache and invocation_id:{invocation_id}", level="info")
            # 从缓存返回时也要使用yield，保持流式接口一致性
            yield extract_result_from_redis
            return

        # 构建查询语句
        new_query = "用户从{}出发，目的地是{}，出发日期为{}，为期{}天，{}往返，{}个人，旅行偏好是{}，行程调整内容为{}。请你制定一个旅行攻略".format(
            source,
            destination,
            travel_start_date,
            traveling_days,
            type,
            people,
            preference,
            modify_content
        )

        # 发送初始进度更新
        yield {
            "status": "progress",
            "desc": "🚀 开始生成旅行规划，请稍候...",
            "progress": 10
        }

        # 异步执行规划生成
        planning_task = asyncio.to_thread(doubao_planning, query=new_query)

        # 发送进度更新
        yield {
            "status": "progress",
            "desc": "🧠 正在分析目的地信息和生成行程...",
            "progress": 30
        }

        # 等待规划生成完成
        planning = await planning_task
        if isinstance(planning, str):
            try:
                planning = json.loads(planning)
            except json.JSONDecodeError as e:
                log_to_file(f"JSON parsing error: {str(e)}", level="error")
                yield {
                    "status": "error",
                    "desc": "❌ 规划数据格式错误",
                    "slots": slots
                }
                return

        if not planning or not isinstance(planning, dict):
            log_to_file("Planning data is invalid or empty", level="error")
            yield {
                "status": "error",
                "desc": "❌ 旅行规划生成失败",
                "slots": slots
            }
            return

        # 发送进度更新
        yield {
            "status": "progress",
            "desc": "🏨 正在获取酒店和景点详细信息...",
            "progress": 50
        }

        # 定义异步辅助函数
        async def update_accommodations_and_attractions():
            """异步更新酒店和景点信息"""
            return await asyncio.to_thread(add_accommodations_and_attractions_info, planning=planning)

        async def update_budget_info(planning_data):
            """异步更新预算信息"""
            return await asyncio.to_thread(update_budget, planning=planning_data)

        async def get_ticket_info(planning_data):
            """异步获取票务信息"""
            return await asyncio.to_thread(get_ticket_information, planning=planning_data)

        try:
            # 更新酒店景点信息
            final_planning = await update_accommodations_and_attractions()
            if not final_planning:
                raise ValueError("酒店景点信息更新失败")

            # 发送进度更新
            yield {
                "status": "progress",
                "desc": "💰 正在计算预算和获取票务信息...",
                "progress": 70
            }

            # 并行执行预算更新和票务信息获取
            budget_task = update_budget_info(final_planning)
            ticket_task = get_ticket_info(final_planning)

            # 等待预算更新完成
            final_planning = await budget_task
            if not final_planning:
                raise ValueError("预算估计更新失败")

            # 等待票务信息获取完成
            ticket_info = await ticket_task
            final_planning["ticket_info"] = ticket_info

            # 发送进度更新
            yield {
                "status": "progress",
                "desc": "📝 正在生成最终旅行报告...",
                "progress": 90
            }

            # 生成markdown
            markdown_str = await asyncio.to_thread(generate_markdown, data=final_planning)

            log_to_file(f"Final planning completed for user {user_id}", level="info")

            # 返回最终结果
            yield {
                "status": "final",
                "desc": "{}".format(markdown_str),
                "tool_result": final_planning,
                "slots": slots
            }

        except Exception as e:
            log_to_file(f"Planning update error: {str(e)}", level="error")
            # 使用基础planning继续处理
            try:
                yield {
                    "status": "progress",
                    "desc": "⚠️ 遇到一些问题，正在尝试生成基础报告...",
                    "progress": 80
                }

                markdown_str = await asyncio.to_thread(generate_markdown, data=planning)
                yield {
                    "status": "final",
                    "desc": "{}".format(markdown_str),
                    "tool_result": planning,
                    "slots": slots
                }
            except Exception as fallback_error:
                log_to_file(f"Fallback markdown generation failed: {str(fallback_error)}", level="error")
                yield {
                    "status": "error",
                    "desc": "❌ 旅行规划生成失败",
                    "slots": slots
                }

    except Exception as e:
        log_to_file(f"Planning function error: {str(e)}", level="error")
        traceback.print_exc()
        yield {
            "status": "error",
            "desc": "❌ 系统错误，请稍后重试",
            "slots": {}
        }


model = doubao_model
instruction = """你是一个优秀的AI助手，熟练掌握thinking step by step方式，你的任务是帮用户进行旅行规划。  
旅行规划共支持8个参数：
    - source（出发地，必填），出发地获取严格按照下面顺序：
        -- 当用户明确表达“X出发，Y三日游”时，直接取X作为出发地。例如，北京出发，来一个成都两日游的旅行攻略，source=北京。
        -- 当用户未明确表达，且用户当前城市不是未知，而是北京、上海、成都等明确的城市名称时，取用户当前城市为出发地。例如，用户表述来一个成都两日游的旅行攻略，用户当前城市为天津，source=天津。
        -- 当用户未明确表达，且用户当前城市未知，则主动询问用户：“要从哪个城市出发呢？”（仅9字，不要生成多余说明或推理文字） 例如，用户表述来一个成都两日游的旅行攻略，用户当前城市未知，发起多轮交互询问用户“要从哪个城市出发呢？”
    - destination（目的地，必填）：
        -- 如果用户已明确给出目的地，直接提取。例如：“给我制定一个北京两日游旅行攻略” → 目的地：北京。
        -- 如果用户说“X周边城市Y日游”，需推理并选择X的一个周边城市作为目的地。例如：“北京周边城市一日游”，推理北京周边有天津、廊坊、涿州等城市，任选其一作为目的地，如选择涿州作为目的地。
        -- 如果用户未说明目的地，则询问：“您想去哪个城市呀？”（仅8字，不要生成多余说明或推理文字）
    - travel_start_date（开始日期，可选，格式yyyy-mm-dd）：默认为“明天”；未指定时使用默认，无需询问。
    - traveling_days（游玩天数，必填，整数）：当用户未表明游玩天数或根据上下文对话无法识别到天数时，你需要询问用户：“打算玩几天呢？”（仅6字，不要生成多余说明或推理文字）
    - type（票务类型，可选，枚举值，值为{“机票”、“火车票”、“公共交通”}）。你需要根据出发地和目的地自行判断出行票务类型。
        -- 当出发地与目的地相同：选择公共交通（如北京→北京）
        -- 当属于跨国旅行（不同国家城市）：选择机票（如北京→东京）。
        -- 当属于同一国家近距离（城市间距离较近）：选择火车票（如北京→秦皇岛）。
        -- 当属于同一国家远距离（城市间距离较远）：选择机票（如北京→上海）。
    - people（旅行人数，可选，默认为1）。未指定时使用1，无需询问。
    - preference（旅行偏好，可选，默认为""）。
        -- 如果用户明确说明旅行偏好，直接提取。例如，“给我制定一个北京两人游攻略，我喜欢人文景观” -> preference=人文景观
        -- 如果用户未说明，无需询问，取默认值""即可
    - modify_content（针对生成的旅行报告进行局部修改，可选，默认为""）
        -- 目前只支持对生成的旅行报告进行行程安排的调整，体现为：换种徒步风格的行程安排、增加某个景点、删除某个景点；当用户表达其他类型的行程调整时，你直接说 不支持即可。
            --- 例1：“帮我把第3天的鼓浪屿改成南普陀寺” ⇒ modify_content = “第3天：删除鼓浪屿，添加南普陀寺”
            --- 例2：“想在第2天去逛天津大学” ⇒ modify_content = “第2天：新增天津大学”
            --- 例3：“我想多去爬山，调整下行程安排” ⇒ modify_content = “多去爬山，调整下行程安排”
        -- 如果用户未说明，无需询问，取默认值""即可

请注意⚠️：
1. 当无法判断“出发地”、“目的地”、“游玩天数”时，发起多轮对话，询问用户。
    -- 询问用户时必须遵循下列3点
        -- 绝对禁止生成任何解释、日期或其它推理逻辑或说明文字，尤其询问天数时
        -- 绝对禁止要求用户按特定格式回复，尤其是日期
        -- 禁止暴露系统后台处理过程
2. 当用户上下文语义完整（包含必填参数）时，你需要将旅行规划的8个参数和对应参数值，拼接成一句语义完整的自然语言描述。
3. 无论是初次生成旅行规划，还是对已有旅行报告进行 modify_content 的调整。你将这句自然语言描述作为参数 query，并依次调用“cot工具、attraction工具、planning工具”。
    -- 调用cot工具，生成旅行攻略的规划思考过程
    -- 调用attraction工具，搜索旅行目的地的景点信息
    -- 调用planning工具，生成完整的旅行规划
"""

travel_rec_agent = LlmAgent(
    name="travel_rec_agent",
    model=model,
    description="旅行规划",
    instruction=instruction,
    tools=[
        today,
        cot,
        attraction,
        # tips,
        planning
    ],
    before_agent_callback=[log_before_agent],
    after_agent_callback=[log_after_agent],
    before_model_callback=[log_before_model],
    after_model_callback=[log_after_model],
    before_tool_callback=[log_before_tool_invocation],
    after_tool_callback=[log_after_tool_invocation],
)
