import os
import asyncio
import httpx
from dotenv import load_dotenv
from google.adk.tools import ToolContext
import traceback
from travel_planner.agent_loggers import log_to_file
from travel_planner.tools.memory import match_latest_slot_value

load_dotenv()
from google.adk import Agent
from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from travel_planner.config import (
    doubao_model,
    qwen_model,
    gemini_model
)
from travel_planner.tools.get_date import (
    today,
    tomorrow
)
from travel_planner.tools.hotel_search import search, search_async
from travel_planner.agent_loggers import (log_before_agent, log_after_agent,
                                          log_before_model, log_after_model,
                                          log_before_tool_invocation, log_after_tool_invocation,
                                          )
"""
产品预定话术：
    为您精选三种酒店方案：
    💰 性价比之选 ｜✨ 高端奢华 ｜👍 高评分口碑款
    快来点击卡片查看详情吧！

    {
        "top30_hotels_by_economic": [],
        "top30_hotels_by_rating": [],
        "top30_hotels_by_luxury": []
    }
"""


async def book_hotel(
    keyword: str = "酒店",
    destination: str = "",
    tool_context: ToolContext = None,
    start_date: str = today(),
    end_date: str = tomorrow(),
    *args,
    **kwargs
):
    try:
        # 先查询redis
        session = tool_context._invocation_context.session
        invocation_id = tool_context.invocation_id
        user_id = session.user_id
        slots = {
            "destination": destination,
            "keyword": keyword
        }
        extract_result_from_redis = match_latest_slot_value(user_id=user_id,
                                                            chunk_code="EndChunkHotelJson", slots=slots)

        log_to_file(f"hotel match from redis:{extract_result_from_redis}", level="info")
        if extract_result_from_redis:
            log_to_file(f"hotel match from cache and invocation_id:{invocation_id}", level="info")
            return extract_result_from_redis

        hotel_result = await asyncio.to_thread(search, keywords=keyword, city=destination)
        # 业务逻辑: 如果性价比之选里没有结果 填充豪华酒店
        if len(hotel_result["top30_hotels_by_luxury"]) > 0 and len(hotel_result["top30_hotels_by_economic"]) == 0:
            hotel_result["top30_hotels_by_economic"] = hotel_result["top30_hotels_by_luxury"]

        valid_rec_nums = 0
        valid_rec_desc = []
        rec_type_dict = {
            "top30_hotels_by_economic": "💰 性价比之选",
            "top30_hotels_by_rating": "👍 高评分口碑款",
            "top30_hotels_by_luxury": "✨ 高端奢华 "
        }
        for key, value in hotel_result.items():
            if len(value) > 0:
                valid_rec_nums += 1
                valid_rec_desc.append(rec_type_dict[key])

        if destination == "未知":
            destination = "🏨"
        if valid_rec_nums == 0:
            desc = """{} {}，没有找到合适的酒店""".format(destination, keyword)
        else:
            desc = """{} {}，为您精选{}种酒店方案：
                    {}
                    快来点击卡片查看详情吧！""".format(destination, keyword, valid_rec_nums, "|".join(valid_rec_desc))

        return {
            "status": "final",
            "desc": desc,
            "tool_result": hotel_result,
            "slots": slots
        }
    except:
        traceback.print_exc()
        return {}


model = doubao_model
instruction = """你是一个优秀的AI助手，熟练掌握thinking step by step方式，你的任务是帮用户进行酒店推荐。
酒店推荐有四个参数：
    - destination（目的城市，必填），目的城市获取严格按照下面顺序：
        -- 当用户明确表达“帮我在X订个酒店”时，直接取X作为目的城市。例如，帮我在北京订个酒店，destination=北京。
        -- 当用户未明确表达，但用户请求中提到“抵达后”、“到达后”或“在当地”，且上下文中存在行程，请理解为在激活行程的目的地提供服务。
        -- 当用户未明确表达，且用户当前城市不是未知，而是北京、上海、成都等明确的城市名称时，取用户当前城市为目的城市。例如，用户表述帮我订个酒店，上下文知用户当前城市为天津，destination=天津。
        -- 当用户未明确表达，且用户当前城市未知，则主动询问用户：“想订哪个城市的酒店呢？” 例如，用户表述帮我订个酒店，上下文知用户当前城市未知，发起多轮交互询问用户“想在哪订酒店”。
    - keyword（关键词，可选，默认为酒店）
        -- 当用户语义表达帮我订个酒店时，keyword=酒店
        -- 当用户语义表达帮我订个如家酒店时，keyword=如家酒店
        -- 当用户语义表达帮我在首钢园附近订个酒店时，keyword=首钢园附近酒店
    - start_date（入住日期，可选，格式为yyyy-mm-dd）：默认为“今天”，当用户未表明入住日期时，无需询问，取默认值即可。
    - end_date（离店日期，可选，格式为yyyy-mm-dd）：默认为“明天”，当用户未表明离店日期时，无需询问，取默认值即可。

请注意⚠️：
1. 首先调用“today”工具获取今天日期，格式为yyyy-mm-dd。
2. 当无法判断目的城市（目的城市未知）时，进行多轮交互，询问“想要订哪个城市的酒店？”
    询问必填参数时：
       -- 绝对禁止生成任何解释、推理或说明文字
3. 当用户上下文语义完整（包含必填参数）时，调用“book_hotel”工具推荐酒店。"""

hotel_agent = LlmAgent(
    name="hotel_agent",
    model=model,
    description="订酒店",
    instruction=instruction,
    tools=[today, book_hotel],
    before_agent_callback=[log_before_agent],
    after_agent_callback=[log_after_agent],
    before_model_callback=[log_before_model],
    after_model_callback=[log_after_model],
    before_tool_callback=[log_before_tool_invocation],
    after_tool_callback=[log_after_tool_invocation],
)
