import json
import traceback
import asyncio
from openai import OpenAI
from typing import Dict
from datetime import datetime
# from prompt import (
#     TRAVEL_PLANNING_COT_PROMPT,
#     TRAVEL_PLANNING_ATTRACTIONS_PROMPT,
#     TRAVEL_PLANNING_TIPS_PROMPT
# )#fro 单测
from travel_planner.tools.prompt import (
    TRAVEL_PLANNING_COT_PROMPT,
    TRAVEL_PLANNING_ATTRACTIONS_PROMPT,
    TRAVEL_PLANNING_TIPS_PROMPT
)

def doubao_llm(query: str, prompt: str, output: str = "dict"):
  try:
      prompt = prompt.replace("{{query}}", query)
      print("Total Prompt: {}".format(prompt))

      client = OpenAI(
          api_key="673d2aa3-5554-46dd-ab47-a0d5a48d6782", # API Key
          base_url="https://ark.cn-beijing.volces.com/api/v3",
      )
      completion = client.chat.completions.create(
          model="ep-20250624110616-w9wxw", # model list：https://www.volcengine.com/docs/82379/1330310#474f7dec
          messages=[
              {'role': 'system', 'content': '你是一个优秀的AI助手，熟练掌握“thinking step by step”的思维方式。'},
              {'role': 'user', 'content': prompt}
          ]
      )
      print("Output: {}".format(completion.choices[0].message.content))
      
      if output == "dict":
        return json.loads(completion.choices[0].message.content)
      return completion.choices[0].message.content
  except:
      traceback.print_exc()
      return None
    

def generate_cot(query: str, prompt: str = TRAVEL_PLANNING_COT_PROMPT):
    return doubao_llm(
        query=query,
        prompt=prompt,
        output="str"
    )

def generate_attraction(query: str, prompt: str = TRAVEL_PLANNING_ATTRACTIONS_PROMPT):
    return doubao_llm(
        query=query,
        prompt=prompt
    )

def generate_tip(query: str, prompt: str = TRAVEL_PLANNING_TIPS_PROMPT):
    return doubao_llm(
        query=query,
        prompt=prompt
    )


# 异步版本的LLM调用函数
async def async_doubao_llm(query: str, prompt: str, output: str = "dict"):
    """异步版本的LLM调用"""
    try:
        # 在线程池中执行同步的LLM调用
        result = await asyncio.to_thread(doubao_llm, query, prompt, output)
        return result
    except Exception as e:
        print(f"异步LLM调用失败: {e}")
        traceback.print_exc()
        return None


async def async_generate_cot(query: str, prompt: str = TRAVEL_PLANNING_COT_PROMPT):
    """异步生成COT"""
    return await async_doubao_llm(query=query, prompt=prompt, output="str")


async def async_generate_attraction(query: str, prompt: str = TRAVEL_PLANNING_ATTRACTIONS_PROMPT):
    """异步生成景点信息"""
    return await async_doubao_llm(query=query, prompt=prompt)


async def async_generate_tip(query: str, prompt: str = TRAVEL_PLANNING_TIPS_PROMPT):
    """异步生成旅行提示"""
    return await async_doubao_llm(query=query, prompt=prompt)


async def generate_all_parallel(query: str) -> Dict:
    """并行生成COT、景点和提示信息"""
    try:
        print("开始并行生成COT、景点和提示信息...")

        # 并行执行三个LLM调用
        tasks = [
            async_generate_cot(query),
            async_generate_attraction(query)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        cot_result, attractions_result = results

        # 处理异常结果
        if isinstance(cot_result, Exception):
            print(f"COT生成失败: {cot_result}")
            cot_result = None

        if isinstance(attractions_result, Exception):
            print(f"景点生成失败: {attractions_result}")
            attractions_result = None

        return {
            "cot": cot_result,
            "attractions": attractions_result
        }

    except Exception as e:
        print(f"并行生成失败: {e}")
        traceback.print_exc()
        return {
            "cot": None,
            "attractions": None,
            "tips": None
        }


if __name__=="__main__":
    import time
    query = "北京出发，6.1开始的为期6天的川西小环线自驾游旅行计划"
    function_list = [
        generate_cot,
        generate_attraction,
        generate_tip
    ]
    prompt_list = [
        TRAVEL_PLANNING_COT_PROMPT,
        TRAVEL_PLANNING_ATTRACTIONS_PROMPT,
        TRAVEL_PLANNING_TIPS_PROMPT
    ]

    total_planning = {}
    for (prompt, func) in zip(prompt_list, function_list):
        st = time.time()
        result = func(query=query, prompt=prompt)
        print("time-cost: {}".format(time.time() - st))
        if prompt != TRAVEL_PLANNING_COT_PROMPT:
          total_planning.update(result)

    print("\n\n\n")
    print(total_planning)