from google.adk.tools import ToolContext

from travel_planner import redis_utils
from travel_planner.agent_loggers import log_to_file

# 旅游规划总结模板
travel_summary_template = """
# 用户发话：
{question}

# 回复摘要：

## 🎯 行程概述
{overview_description}

{overview_items}  

---

## 🏨 精选住宿推荐
{accommodation_list}

---

## 📆 每日行程规划
{itinerary_list}

---

## ⭐ 重点景点推荐
{attractions_list}

"""

# 火车票总结模板
train_ticket_summary_template = """
# 用户发话：
{question}

# 回复摘要：

{source} → {destination} | {date}
{results_summary}
"""

# 机票总结模板
plane_ticket_summary_template = """
# 用户发话：
{question}

# 回复摘要：

{source} → {destination} ({date})

💰 最便宜: {cheapest_airline}{cheapest_flightNo}
   | 起飞: {cheapest_depTime} @ {cheapest_depAirport}
   | 到达: {cheapest_arrTime} @ {cheapest_arrAirport}
   | 价格: ¥{cheapest_price} [预订]({cheapest_link})

⏩ 最快: {shortest_airline}{shortest_flightNo}
   | 起飞: {shortest_depTime} @ {shortest_depAirport}
   | 到达: {shortest_arrTime} @ {shortest_arrAirport}
   | 价格: ¥{shortest_price} [预订]({shortest_link})

🌅 最早: {earliest_airline}{earliest_flightNo}
   | 起飞: {earliest_depTime} @ {earliest_depAirport}
   | 到达: {earliest_arrTime} @ {earliest_arrAirport}
   | 价格: ¥{earliest_price} [预订]({earliest_link})

"""

# 酒店总结模板
hotel_summary_template = """
# 用户发话：
{question}

# 回复摘要：
🏨 {location}酒店精选 | {date}

{hotel_list}

"""


def parse_travel_recommendation(question, answer):
    tool_result = answer['tool_result']

    # 基础信息
    title = tool_result["title"]
    subtitle = tool_result["subtitle"]
    date_range = tool_result["date_range"]
    route = tool_result["route"]

    # 行程概述
    overview = tool_result["overview"]
    overview_description = overview["description"]
    overview_items = "\n".join([
        f"- {item['title']}: {item['content']}"
        for item in overview["items"]
    ])

    # 住宿推荐（取前3个）
    accommodation_list = "\n".join([
        f"🏩 **{acc['name']}**（评分: {acc['rating'] if acc.get('rating') else ''}）\n"
        f"  - 特色: {', '.join(acc['features'])}\n"
        f"  - {acc['description']}"
        for acc in tool_result["accommodation"][:3]
    ])

    # 每日行程
    itinerary_list = []
    for day in tool_result["itinerary"]:
        day_str = f"**第{day['day']}天: {day['date']} {day['title']}**\n"
        for schedule in day["schedule"]:
            day_str += f"  - {schedule['time']}: {schedule['title']}\n"
        itinerary_list.append(day_str)
    itinerary_list = "\n".join(itinerary_list)

    # 景点推荐（取前3个）
    attractions_list = "\n".join([
        f"⭐ **{att['name']}**（评分: {att['rating']}）\n"
        f"  - {att['description']}\n"
        f"  - 开放时间: {att.get('opentime_week', '全天')}"
        for att in tool_result["attractions"][:3]
    ])

    # # 机票信息
    # tickets = tool_result["ticket_info"]
    # dep = tickets["departure"]
    # ret = tickets["return"]
    #
    # dep_cheapest = f"{dep['cheapest_price'][0]['flightCompany']}{dep['cheapest_price'][0]['flightNo']} " \
    #                f"{dep['cheapest_price'][0]['depTime']}→{dep['cheapest_price'][0]['arrTime']}"
    # dep_cheapest_price = dep["cheapest_price"][0]["price"]
    #
    # dep_shortest = f"{dep['shortest_totaltime'][0]['flightCompany']}{dep['shortest_totaltime'][0]['flightNo']} " \
    #                f"{dep['shortest_totaltime'][0]['depTime']}→{dep['shortest_totaltime'][0]['arrTime']}"
    # dep_shortest_price = dep["shortest_totaltime"][0]["price"]
    #
    # return_cheapest = f"{ret['cheapest_price'][0]['flightCompany']}{ret['cheapest_price'][0]['flightNo']} " \
    #                   f"{ret['cheapest_price'][0]['depTime']}→{ret['cheapest_price'][0]['arrTime']}"
    # return_cheapest_price = ret["cheapest_price"][0]["price"]

    # 页脚信息
    result = travel_summary_template.format(
        question=question,
        title=title,
        subtitle=subtitle,
        date_range=date_range,
        route=route,
        overview_description=overview_description,
        overview_items=overview_items,
        accommodation_list=accommodation_list,
        itinerary_list=itinerary_list,
        attractions_list=attractions_list,
    )
    return result


def parse_flight_summary(question, answer, template=plane_ticket_summary_template):
    """基于模板生成机票总结"""
    try:
        # 提取基础数据
        tool_result = answer["tool_result"]
        source = tool_result["source"]
        destination = tool_result["destination"]
        date = tool_result["date"]
        url = tool_result["url"]

        # 提取航班详情
        details = tool_result["details"]
        cheapest = details.get("cheapest_price", [])
        cheapest = cheapest[0] if cheapest else {}
        shortest = details.get("shortest_totaltime", [])
        shortest = shortest[0] if shortest else {}
        earliest = details.get("earliest_starttime", [])
        earliest = earliest[0] if earliest else {}
        # 准备模板填充数据
        template_data = {
            "question": question,
            "source": source,
            "destination": destination,
            "date": date,
            "url": url,
            # 最优惠方案
            "cheapest_airline": cheapest.get("flightCompany", ""),
            "cheapest_flightNo": cheapest.get("flightNo", ""),
            "cheapest_depTime": cheapest.get("depTime", ""),
            "cheapest_depAirport": cheapest.get("depAirport", ""),
            "cheapest_arrTime": cheapest.get("arrTime", ""),
            "cheapest_arrAirport": cheapest.get("arrAirport", ""),
            "cheapest_price": cheapest.get("price", ""),
            "cheapest_link": cheapest.get("h5Url", ""),
            # 最短时间方案
            "shortest_airline": shortest.get("flightCompany", ""),
            "shortest_flightNo": shortest.get("flightNo", ""),
            "shortest_depTime": shortest.get("depTime", ""),
            "shortest_depAirport": shortest.get("depAirport", ""),
            "shortest_arrTime": shortest.get("arrTime", ""),
            "shortest_arrAirport": shortest.get("arrAirport", ""),
            "shortest_price": shortest.get("price", ""),
            "shortest_link": shortest.get("h5Url", ""),
            # 最早起飞方案
            "earliest_airline": earliest.get("flightCompany", ""),
            "earliest_flightNo": earliest.get("flightNo", ""),
            "earliest_depTime": earliest.get("depTime", ""),
            "earliest_depAirport": earliest.get("depAirport", ""),
            "earliest_arrTime": earliest.get("arrTime", ""),
            "earliest_arrAirport": earliest.get("arrAirport", ""),
            "earliest_price": earliest.get("price", ""),
            "earliest_link": earliest.get("h5Url", "")
        }

        # 应用模板
        return template.format(**template_data)

    except Exception as e:

        return f"生成总结时出错: {str(e)}"


def parse_train_summary(question, answer):
    """格式化火车票信息总结"""
    train_details_template = """ 
        🎫 {train_type}:
        - 车次: {train_code}
        - 时间: {dep_time} → {arr_time} ({duration})
        - 座位: {seat_info}
        - 价格: ¥{price}
        - [预订]({booking_link})
    """
    # 提取基础数据
    try:
        tool_result = answer["tool_result"]
        source = tool_result["source"]
        destination = tool_result["destination"]
        date = tool_result["date"]
        url = tool_result["url"]

        # 提取车次信息
        details = tool_result["details"]
        cheapest_list = details["cheapest_price"]
        fastest_list = details["shortest_totaltime"]
        earliest_list = details["earliest_starttime"]

        # 判断是否有结果
        no_results = not any([cheapest_list, fastest_list, earliest_list])

        if no_results:
            return train_ticket_summary_template.format(
                question=question,
                source=source,
                destination=destination,
                date=date,
                results_summary="",
                url=url
            )

        # 有结果时生成结果详情
        details_summary = []

        # 最便宜车次
        if cheapest_list:
            cheapest = cheapest_list[0]
            details_summary.append(train_details_template.format(
                train_type="最经济实惠",
                train_code=cheapest.get("trainCode", "未知车次"),
                dep_time=cheapest.get("depTime", "未知"),
                arr_time=cheapest.get("arrTime", "未知"),
                duration=cheapest.get("totalTime", "未知"),
                seat_info=f"{cheapest.get('seatType', '未知')}座",
                price=cheapest.get("price", "未知"),
                booking_link=cheapest.get("h5Url", url)
            ))

        # 最快速车次
        if fastest_list:
            fastest = fastest_list[0]
            details_summary.append(train_details_template.format(
                train_type="最省时高效",
                train_code=fastest.get("trainCode", "未知车次"),
                dep_time=fastest.get("depTime", "未知"),
                arr_time=fastest.get("arrTime", "未知"),
                duration=fastest.get("totalTime", "未知"),
                seat_info=f"{fastest.get('seatType', '未知')}座",
                price=fastest.get("price", "未知"),
                booking_link=fastest.get("h5Url", url)
            ))

        # 最早车次
        if earliest_list:
            earliest = earliest_list[0]
            details_summary.append(train_details_template.format(
                train_type="最早班次",
                train_code=earliest.get("trainCode", "未知车次"),
                dep_time=earliest.get("depTime", "未知"),
                arr_time=earliest.get("arrTime", "未知"),
                duration=earliest.get("totalTime", "未知"),
                seat_info=f"{earliest.get('seatType', '未知')}座",
                price=earliest.get("price", "未知"),
                booking_link=earliest.get("h5Url", url)
            ))

        return train_ticket_summary_template.format(
            question=question,
            source=source,
            destination=destination,
            date=date,
            results_summary="\n".join(details_summary),
            url=url
        )

    except Exception as e:
        error_msg = f"解析火车票数据时出错: {str(e)}"
        return train_ticket_summary_template.format(
            source="未知",
            destination="未知",
            date="未知",
            results_summary=f"⚠️ 错误: {error_msg}",
            url="#"
        )


def parse_hotel_summary(question, answer):
    """生成酒店信息简洁总结"""
    HOTEL_ITEM_TEMPLATE = """
    ⭐ {name} - ⭐{rating}
    📍 {address}
    📞 {phone} | 🏷️ {features}
    """
    try:
        # 提取基础数据
        tool_result = answer["tool_result"]
        hotels = tool_result["top30_hotels_by_economic"]

        # 获取位置信息（使用第一个酒店的省份）
        location = hotels[0]["pname"] if hotels else "未知地区"

        # 获取当前日期作为日期范围
        from datetime import datetime
        date = datetime.now().strftime("%Y-%m-%d")

        # 生成酒店列表
        hotel_list = []
        for i, hotel in enumerate(hotels[:3]):  # 只取前3个酒店
            business = hotel.get("business", {})

            hotel_list.append(HOTEL_ITEM_TEMPLATE.format(
                name=hotel["name"],
                rating=business.get("rating", "N/A"),
                address=hotel["address"],
                phone=business.get("tel", "暂无电话").split(";")[0],  # 只取第一个电话
                features=business.get("keytag", business.get("rectag", "暂无特色"))
            ))

        return hotel_summary_template.format(
            question=question,
            location=location,
            date=date,
            hotel_list="\n".join(hotel_list) or "⚠️ 未找到符合条件的酒店",
        )

    except Exception as e:
        return f"生成酒店总结时出错: {str(e)}"


def memory_summary(tool_context: ToolContext):
    """
    基于上下文最新10轮的对话总结
    :param tool_context: 工具上下文对象，用于获取session中的user_id
    :return:
    """
    session = tool_context._invocation_context.session
    user_id = session.user_id
    memory_list = redis_utils.query_memory(
        f"multi_agents:travel_planner:memory:{user_id}")
    index = 1
    result = "上下文内容如下:\n"
    result += "================\n"
    max_summary: str = ""
    # 建议正序
    memory_list = memory_list[::-1]
    for memory in memory_list:
        question = memory.get("question")
        answer = memory.get("answer")
        chunk_code = memory.get("chunk_code")
        summary = ""
        if chunk_code == "EndChunkTravelJson":
            summary = parse_travel_recommendation(question, answer)
            max_summary = max(max_summary, summary)
        if chunk_code == "EndChunkPlaneTicketURL":
            summary = parse_flight_summary(question, answer)
        if chunk_code == "EndChunkTrainTicketURL":
            summary = parse_train_summary(question, answer)
        if chunk_code == "EndChunkHotelJson":
            summary = parse_hotel_summary(question, answer)
        result += f"\n第{index}轮记忆："
        result += summary
        index += 1

    result += "================"
    log_to_file(f"{user_id}_memory_summary:\n{result}", level="info")
    return result


def match_latest_slot_value(user_id: str, chunk_code: str, slots: dict) -> dict:
    """
    基于user+chunk_code匹配最新的槽位值
    :param slots:
    :param user_id:
    :param chunk_code
    :return: dict
    """
    memory_list = redis_utils.query_memory(f"multi_agents:travel_planner:memory:{user_id}")
    if not memory_list:
        return {}
    for memory in memory_list:
        chunk_code_db = memory.get("chunk_code")
        if chunk_code == chunk_code_db:
            memory_slots = memory.get("answer").get("slots")
            # 按照sub-agent中定义的slots顺序
            if slots == memory_slots:
                return memory.get("answer")

    return {}


def get_dialog_context(user_id: str) -> list[dict]:
    """
    获取当前用户的对话上下文
    :param user_id
    :return:
    """
    memory_list = redis_utils.query_memory(f"multi_agents:travel_planner:memory:dialogContext:{user_id}")
    log_to_file(f"{user_id} get dialog context:{memory_list}", level="info")
    if not memory_list:
        return []
    memory_list=memory_list[::-1]
    return memory_list

