import json
import traceback
from openai import OpenAI
from typing import Dict
from travel_planner.tools.hotel_search import (
    keyword_search_top1,
    keyword_search_helper
)
from travel_planner.tools.ticket_search import search_wrapper as train_search
from travel_planner.tools.flignt_search import search_flights_wrapper as flight_search

"""帮我制定一个从北京出发，10.1日开始的厦门5日旅游规划时"""
travel_rec_example = {
  "query_info": {
      "source": "北京",
      "destination": "厦门",
      "travel_start_date": "2025-10-01",
      "travel_end_date": "2025-10-02",
      "traveling_days": "2",
      "type": "机票",
      "people": "1",
  },
  "title": "厦门2日精致旅行",
  "subtitle": "探索海滨城市的浪漫，感受闽南文化的魅力",
  "date_range": "2025/10/01 至 2025/10/02",
  "route": "北京 到 厦门",
  "overview": {
    "description": "厦门，这座闽南海滨城市，拥有独特的海岛风情和浓厚的历史文化底蕴。本次2日精致旅行将带您领略厦门的多彩风貌，从文艺气息浓郁的鼓浪屿，到历史悠久的南普陀寺，再到美丽的环岛路海滨风光，一一尽收眼底。",
    "items": [
      {
        "title": "旅行时间",
        "content": "2天1晚 10月1日-10月2日"
      },
      {
        "title": "行程特色",
        "content": "鼓浪屿文艺之旅 厦门大学人文探索 闽南美食体验"
      },
      {
        "title": "交通方式",
        "content": "北京-厦门往返机票 当地公交/地铁出行"
      }
    ]
  },
  "accommodation": [
    {
      "name": "厦门鼓浪屿精品民宿",
      "city": "厦门",
      "description": "位于鼓浪屿岛上，步行可达各大景点，文艺风格装修，安静舒适，提供免费早餐。适合追求文艺氛围与便捷出行的旅客。",
      "features": [
          "鼓浪屿岛上",
          "文艺风格"
      ]
    },
    {
      "name": "厦门中山路经济型酒店",
      "city": "厦门",
      "description": "位于市中心中山路商业区，交通便利，周边美食街林立，是购物、品尝闽南美食的理想住所。干净整洁，性价比高。",
      "features": [
          "市中心",
          "近美食街",
          "经济实惠"
      ]
    },
    {
      "name": "环岛路海景酒店",
      "city": "厦门",
      "description": "坐拥一线海景，紧邻环岛路，可欣赏日出日落美景。配备健身中心与泳池，客房宽敞明亮，服务周到。适合预算充足的旅客。",
      "features": [
          "一线海景",
          "高端体验"
      ]
    }
  ],
  "itinerary": [
    {
      "day": 1,
      "date": "10月1日",
      "title": "初识厦门，鼓浪屿之旅开始",
      "schedule": [
        {
          "time": "上午",
          "title": "抵达厦门，前往鼓浪屿",
          "details": "抵达厦门高崎机场后，乘坐机场快线或出租车前往轮渡码头。提前通过“厦门轮渡”公众号预定船票，前往鼓浪屿。"
        },
        {
          "time": "中午",
          "title": "入住酒店，享用午餐",
          "details": "抵达鼓浪屿后入住酒店，放置行李后在附近的本地餐厅享用午餐，品尝闽南特色小吃。"
        },
        {
          "time": "下午",
          "title": "游览鼓浪屿核心景点",
          "details": "购买景点联票，游览菽庄花园和日光岩。感受闽南园林建筑的魅力，登上日光岩俯瞰鼓浪屿全景。"
        },
        {
          "time": "晚上",
          "title": "品尝海鲜晚餐",
          "details": "在鼓浪屿当地餐厅享用海鲜小炒晚餐，晚餐后可在岛上漫步，欣赏夜景。"
        }
      ],
      "pois": [
        {
          "name": "厦门高崎机场",
          "city": "厦门",
          "description": "服务国内外航线的现代化机场。",
          "suggestion": "留足安检和登机时间，尤其是假期高峰期。",
          "highlight": "“从候机厅望向跑道，旅人的心也随航班启程。”"
        },
        {
          "name": "菽庄花园",
          "city": "厦门",
          "description": "典型的闽南园林，融合中西建筑风格，充满历史韵味。",
          "suggestion": "建议购买联票，一次性游览所有景点。",
          "highlight": "“穿梭在花园间，仿佛置身于古典与海风交织的仙境。”"
        },
        {
          "name": "日光岩",
          "city": "厦门",
          "description": "鼓浪屿最高点，可俯瞰整个岛屿和厦门海景。",
          "suggestion": "清晨或傍晚登顶，避开正午人流高峰。",
          "highlight": "“登上日光岩，苍茫的大海与远山尽收眼底，让人心旷神怡。”"
        }
      ]
    },
      {
          "day": 2,
          "date": "10月2日",
          "title": "文化探索与购物休闲",
          "schedule": [
              {
                  "time": "上午",
                  "title": "集美学村半日游(可选)",
                  "details": "乘地铁前往集美学村(免费)，参观嘉庚建筑群，了解陈嘉庚先生教育贡献。"
              },
              {
                  "time": "中午",
                  "title": "集美午餐",
                  "details": "在集美享用地方特色午餐。"
              },
              {
                  "time": "下午",
                  "title": "购买伴手礼",
                  "details": "购买特产，如黄胜记肉松、馅饼、茶叶等。"
              },
              {
                  "time": "晚上",
                  "title": "返程",
                  "details": "前往厦门高崎机场搭乘返程航班，结束愉快的厦门之旅。"
              }
          ],
          "pois": [
              {
                  "name": "集美学村",
                  "city": "厦门",
                  "description": "陈嘉庚先生主持建设的学村，融合中西建筑风格。",
                  "suggestion": "可提前预约讲解服务，了解更多历史故事。",
                  "highlight": "“在洋楼与红砖间，仿佛听见百年前的求知足音。”"
              },
              {
                  "name": "厦门高崎机场",
                  "city": "厦门",
                  "description": "服务国内外航线的现代化机场。",
                  "suggestion": "留足安检和登机时间，尤其是假期高峰期。",
                  "highlight": "“从候机厅望向跑道，旅人的心也随航班启程。”"
              }
          ]
      }
  ],
  "tips": [
    "厦门气候宜人，10月平均温度20-28°C，建议带轻薄外套，注意防晒。",
    "厦门公交/地铁便捷，建议下载“厦门公交”APP或使用电子交通卡。",
    "鼓浪屿船票建议提前通过“厦门轮渡”公众号预订（35元/人），避免排队。",
    "厦门大学参观需提前3天在“厦大预约系统”预约，并携带身份证。",
    "鼓浪屿和中山路是购买特产的好地方，常见特产包括黄胜记肉松、馅饼和茶叶。"
  ],
  "footer": {
    "text": ["厦门2日精致旅行规划", "祝您旅途愉快！"]
  },
  "budget": {
    "transportation": {
      "description": "北京到厦门机票（往返）",
      "estimate_min": 1000,
      "estimate_max": 1600
    },
    "accommodation": {
      "description": "4晚酒店入住",
      "estimate_min": 800,
      "estimate_max": 1600
    },
    "attraction": {
      "description": "鼓浪屿、南普陀寺等景点",
      "estimate_min": 100,
      "estimate_max": 300
    },
    "meal": {
      "description": "5日4晚餐饮",
      "estimate_min": 500,
      "estimate_max": 1000
    }
  }
}

prompt = """你需要给用户生成一份非常详细的旅行计划，结果以JSON格式输出。
例如，当用户语义表达“帮我制定一个从北京出发，10.1日开始的厦门5日旅游规划时”，你制定的旅行计划如下所示。
{{example}}

其中，
overview表示行程概览
accommodation表示住宿推荐
itinerary表示每日行程安排
attractions表示景点介绍
tips表示旅行小贴士
footer表示页脚
budget表示预算估计
  --transportation表示往返交通方式费用预估
  --accommodation表示入住酒店费用预估
  --attraction表示游玩景点费用预估
  --meal表示旅行吃饭餐饮费用预估

请你直接输出JSON格式的旅行计划，不要输出其他任何内容。
现在，请你对“{{query}}”生成一个如上的旅行计划""".replace("{{example}}", json.dumps(travel_rec_example, ensure_ascii=False, indent=2))

def doubao_planning(query: str, prompt: str = prompt):
    prompt = prompt.replace("{{query}}", query)
    print("Total Prompt: {}".format(prompt))

    client = OpenAI(
        api_key="673d2aa3-5554-46dd-ab47-a0d5a48d6782", # API Key
        base_url="https://ark.cn-beijing.volces.com/api/v3",
    )
    completion = client.chat.completions.create(
        model="ep-20250624110616-w9wxw", # model list：https://www.volcengine.com/docs/82379/1330310#474f7dec
        messages=[
            {'role': 'system', 'content': '你是一个优秀的AI助手，熟练掌握“thinking step by step”的思维方式。'},
            {'role': 'user', 'content': prompt}
        ]
    )
    print("Output: {}".format(completion.choices[0].message.content))
    return completion.choices[0].message.content

"""
"accommodation": [
    {
      "name": "厦门鼓浪屿精品民宿",
      "city": "厦门",
      "description": "位于鼓浪屿岛上，步行可达各大景点，文艺风格装修，安静舒适，提供免费早餐。适合追求文艺氛围与便捷出行的旅客。",
      "features": [
          "鼓浪屿岛上",
          "文艺风格"
      ]
    }
]

"itinerary": [
  {
    "pois": [
      {
        "name": "厦门园林植物园",
        "city": "厦门",
        "description": "集热带植物展示与生态科普于一体的大型园区。",
        "suggestion": "建议带上驱蚊用品，以防高温季节蚊虫叮咬。",
        "highlight": "“在绿荫环绕中，暂别城市喧嚣，尽享自然清新。”"
      }
    ]
  }
]

attractions": [
    {
      "name": "鼓浪屿",
      "city": "厦门",
      "description": "被誉为“钢琴之岛”和“万国建筑博物馆”，拥有独特历史风貌和文化底蕴。",
      "suggestion": "建议在岛上住宿至少一晚，避开周末人流高峰，早上和傍晚游览最佳。",
      "highlight": "“漫步在鼓浪屿的石板路上，仿佛穿越回百年前的国际文化交融时光。”"
    }
]
"""
def add_accommodations_and_attractions_info(planning: Dict):
    try:
        # 酒店
        accommodations = planning["accommodation"]
        new_accommodations = []

        for accommodation in accommodations:
            name = accommodation["name"]
            city = accommodation["city"]
            description =  accommodation["description"]
            features = accommodation["features"]

            pois = keyword_search_helper(keywords=name, region=city, page_size="3")
            if len(pois) == 0:
                new_accommodations.append(accommodation)
                continue
            
            for poi in pois:
                tmp = {
                    "name": name,
                    "city": city,
                    "description": description,
                    "features": features
                }
                poi_name = poi.get("name", "")
                rating = poi.get('business', {}).get('rating', "")
                url = poi.get("photos", [{}])[0].get("url", "")
                # 获取高德rating和url
                if rating:
                    tmp["rating"] = rating
                if url:
                    tmp["url"] = url
                if poi_name:#用搜到的酒店name替换llm生成的酒店
                    tmp["name"] = poi_name
                
                new_accommodations.append(tmp)

        # update planning
        planning["accommodation"] = new_accommodations

        attractions = []
        # update pois信息
        itinerary = planning["itinerary"]
        for each_day in itinerary:
            pois = each_day["pois"]
            for poi in pois:
                name = poi["name"]
                city = poi["city"]

                amap_poi = keyword_search_top1(keywords=name, region=city)
                amap_poi_name = amap_poi["name"]
                amap_poi_rating = amap_poi.get("business", {}).get("rating", "")
                amap_poi_url = amap_poi.get("photos", [{}])[0].get("url", "")
                amap_poi_opentime_week = amap_poi.get("business", {}).get("opentime_week", "")
                
                # update poi
                poi["name"] = amap_poi_name
                poi["rating"] = amap_poi_rating
                poi["url"] = amap_poi_url
                poi["opentime_week"] = amap_poi_opentime_week

                # attractions过滤机场和火车站点
                if "机场" in amap_poi_name or "站" in amap_poi_name:
                    continue
                attractions.append(poi)

        planning["attractions"] = attractions
        try:
            planning["header_background_image_url"] = planning["attractions"][0]["url"]
        except:
            planning["header_background_image_url"] = ""
        return planning
    except:
        traceback.print_exc()
        return planning
  
"""
"budget": {
    "transportation": {
      "description": "上海到北京机票（往返）",
      "estimate_min": 1000,
      "estimate_max": 1600
    },
    "accommodation": {
      "description": "4晚酒店入住",
      "estimate_min": 800,
      "estimate_max": 1600
    },
    "attraction": {
      "description": "鼓浪屿、南普陀寺等景点",
      "estimate_min": 100,
      "estimate_max": 300
    },
    "meal": {
      "description": "5日4晚餐饮",
      "estimate_min": 500,
      "estimate_max": 1000
    }
  }
"""
def update_budget(planning: dict):
    try:
        title =  planning["title"]
        budget = planning["budget"]
        total_estimate_min = 0
        total_estimate_max = 0
        for key, value in budget.items():
            total_estimate_min += value["estimate_min"]
            total_estimate_max += value["estimate_max"]
        
        budget["total_estimate"] = {
            "description": "{}总费用预估".format(title),
            "estimate_min": total_estimate_min,
            "estimate_max": total_estimate_max
        }
        return planning
    except:
        traceback.print_exc()
        return planning

"""
"query_info": {
    "source": "北京",
    "destination": "北京",
    "travel_start_date": "2025-06-12",
    "travel_end_date": "2025-06-14",
    "traveling_days": "3",
    "type": "公共交通",
    "people": "1"
}
"""
def get_ticket_information(planning: dict):
    try:
        query_info = planning["query_info"]
        source = query_info["source"]
        destination = query_info["destination"]
        if source.endswith("市"):
          source =  source[:-1]
        if destination.endswith("市"):
          destination = destination[:-1]
        start_date = query_info["travel_start_date"]
        end_date = query_info["travel_end_date"]
        travel_type = query_info["type"]
        if travel_type == "公共交通":
            return {}
        elif travel_type == "机票":
            return {
                "type": "flight",
                "departure": flight_search(
                    start_city=source,
                    end_city=destination,
                    date=start_date
                ),
                "return": flight_search(
                    start_city=destination,
                    end_city=source,
                    date=end_date
                )
            }
        elif travel_type == "火车票":
            return {
                "type": "train",
                "departure": train_search(
                    start_city=source,
                    end_city=destination,
                    date=start_date
                ),
                "return": train_search(
                    start_city=destination,
                    end_city=source,
                    date=end_date
                )
            }
        else:
            return {}

    except:
        traceback.print_exc()
        return {}

# 旅行报告生成Markdown
def generate_markdown(data: Dict):
    md = []
    # 标题
    title = data.get('title')
    subtitle = data.get('subtitle')
    md.append(f"### 🌆 {title}: {subtitle}\n")

    # 行程总览
    overview = data.get('overview', {})
    desc = overview.get('description', '').strip()
    md.append('#### 🎯 行程总览\n')
    md.append(desc + '\n')

    # 每日行程
    itins = data.get('itinerary', [])
    if itins:
        for day in itins:
            day_no = day.get('day')
            title_day = day.get('title')
            md.append(f"#### 📅 **第 {day_no} 天：{title_day}**\n")

            # 当日行程
            md.append("#### 🌟 当日行程\n")
            for sch in day.get('schedule', []):
                md.append(f"* {sch.get('details')}\n")
            md.append('\n')

    # # 贴心锦囊
    # tips = data.get('tips', [])
    # if tips:
    #     md.append('#### 💡 贴心锦囊\n')
    #     for tip in tips:
    #         md.append(f"* {tip}\n")
    #     md.append('\n')

    return '\n'.join(md)


if __name__=="__main__":
    pass
