"""
性能监控工具
用于监控travel_rec_agent的性能指标
"""
import time
import asyncio
import functools
from typing import Dict, List, Any, Callable
from travel_planner.agent_loggers import log_to_file

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.call_counts: Dict[str, int] = {}
        
    def record_timing(self, function_name: str, duration: float):
        """记录函数执行时间"""
        if function_name not in self.metrics:
            self.metrics[function_name] = []
            self.call_counts[function_name] = 0
            
        self.metrics[function_name].append(duration)
        self.call_counts[function_name] += 1
        
    def get_stats(self, function_name: str) -> Dict[str, Any]:
        """获取函数性能统计"""
        if function_name not in self.metrics:
            return {}
            
        durations = self.metrics[function_name]
        return {
            "count": self.call_counts[function_name],
            "avg_time": sum(durations) / len(durations),
            "min_time": min(durations),
            "max_time": max(durations),
            "total_time": sum(durations)
        }
        
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有函数的性能统计"""
        return {name: self.get_stats(name) for name in self.metrics.keys()}
        
    def log_stats(self):
        """记录性能统计到日志"""
        stats = self.get_all_stats()
        log_to_file(f"Performance Stats: {stats}", level="info")

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(func_name: str = None):
    """性能监控装饰器"""
    def decorator(func: Callable):
        name = func_name or func.__name__
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration = time.perf_counter() - start_time
                    performance_monitor.record_timing(name, duration)
                    log_to_file(f"{name} executed in {duration:.3f}s", level="info")
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.perf_counter() - start_time
                    performance_monitor.record_timing(name, duration)
                    log_to_file(f"{name} executed in {duration:.3f}s", level="info")
            return sync_wrapper
    return decorator

def log_performance_summary():
    """记录性能摘要"""
    performance_monitor.log_stats()

# 性能基准测试工具
class PerformanceBenchmark:
    """性能基准测试"""
    
    @staticmethod
    async def benchmark_planning_pipeline(query: str, slots: Dict):
        """基准测试规划流水线"""
        from travel_planner.subagents.travel_rec_agent import (
            parallel_cot_attraction, 
            planning
        )
        
        # 测试并行COT和景点搜索
        start_time = time.perf_counter()
        cot_attraction_result = await parallel_cot_attraction(query)
        cot_attraction_time = time.perf_counter() - start_time
        
        log_to_file(f"Parallel COT+Attraction time: {cot_attraction_time:.3f}s", level="info")
        
        return {
            "cot_attraction_time": cot_attraction_time,
            "cot_attraction_result": cot_attraction_result
        }
    
    @staticmethod
    async def compare_sequential_vs_parallel(query: str):
        """比较顺序执行vs并行执行的性能"""
        from travel_planner.tools.travel_planning_cot import (
            generate_cot, 
            generate_attraction,
            generate_all_parallel
        )
        
        # 顺序执行
        start_time = time.perf_counter()
        cot_result = await asyncio.to_thread(generate_cot, query=query)
        attraction_result = await asyncio.to_thread(generate_attraction, query=query)
        sequential_time = time.perf_counter() - start_time
        
        # 并行执行
        start_time = time.perf_counter()
        parallel_result = await generate_all_parallel(query)
        parallel_time = time.perf_counter() - start_time
        
        improvement = ((sequential_time - parallel_time) / sequential_time) * 100
        
        log_to_file(f"Sequential time: {sequential_time:.3f}s", level="info")
        log_to_file(f"Parallel time: {parallel_time:.3f}s", level="info")
        log_to_file(f"Performance improvement: {improvement:.1f}%", level="info")
        
        return {
            "sequential_time": sequential_time,
            "parallel_time": parallel_time,
            "improvement_percent": improvement
        }

# 缓存性能监控
class CacheMonitor:
    """缓存性能监控"""
    
    def __init__(self):
        self.hit_count = 0
        self.miss_count = 0
        
    def record_hit(self):
        """记录缓存命中"""
        self.hit_count += 1
        
    def record_miss(self):
        """记录缓存未命中"""
        self.miss_count += 1
        
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hit_count + self.miss_count
        return (self.hit_count / total * 100) if total > 0 else 0
        
    def log_stats(self):
        """记录缓存统计"""
        hit_rate = self.get_hit_rate()
        log_to_file(f"Cache Stats - Hits: {self.hit_count}, Misses: {self.miss_count}, Hit Rate: {hit_rate:.1f}%", level="info")

# 全局缓存监控器
cache_monitor = CacheMonitor()

if __name__ == "__main__":
    # 性能测试示例
    async def test_performance():
        query = "北京出发，成都3日游旅行规划"
        
        # 运行基准测试
        benchmark = PerformanceBenchmark()
        comparison_result = await benchmark.compare_sequential_vs_parallel(query)
        
        print(f"性能对比结果: {comparison_result}")
        
        # 记录性能摘要
        log_performance_summary()
        
        # 记录缓存统计
        cache_monitor.log_stats()
    
    # 运行测试
    asyncio.run(test_performance())
