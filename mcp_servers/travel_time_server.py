import json
import logging
from datetime import datetime, date
from typing import Dict, List
import chinese_calendar as calendar
# from fastmcp import FastMCP
from fastapi_mcp import FastApiMCP
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel


# mcp = FastMCP("travel-time-server", dependencies=["chinese-calendar"])
app = FastAPI()

# 创建 MCP 服务实例，绑定已有 FastAPI 应用
mcp = FastApiMCP(app, name="travel-time-server")
# 挂载 MCP 服务到 FastAPI 应用
mcp.mount()

# @app.get("/")
# async def root():
#     return {
#         "message": "Travel Time MCP Server",
#         "endpoints": {
#             "/is_holiday": "Check if a date is a holiday",
#             "/best_season": "Get best travel seasons by climate",
#             "/check_duration": "Validate trip duration"
#         },
#         "mcp_tools": ["is_holiday", "best_season", "check_duration"]
#     }


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Seasonal recommendations (can be customized)
SEASONAL_RECOMMENDATIONS = {
        'tropical': {
            'best': ['December', 'January', 'February'],
            'good': ['March', 'April', 'November'],
            'avoid': ['May', 'June', 'July', 'August', 'September', 'October']
        },
        'temperate': {
            'best': ['May', 'June', 'September'],
            'good': ['April', 'July', 'August', 'October'],
            'avoid': ['November', 'December', 'January', 'February', 'March']
        },
        'arctic': {
            'best': ['June', 'July', 'August'],
            'good': ['May', 'September'],
            'avoid': ['October', 'November', 'December', 'January', 'February', 'March', 'April']
        },
        'desert': {
            'best': ['November', 'December', 'January', 'February'],
            'good': ['March', 'October'],
            'avoid': ['April', 'May', 'June', 'July', 'August', 'September']
        }
    }

def check_holiday(date_str: str) -> Dict:
    """Check if a date is a holiday."""
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError('Invalid date format. Use YYYY-MM-DD')

    is_holiday = calendar.is_holiday(date_obj)
    return {
        'date': date_str,
        'is_holiday': is_holiday,
        'holiday_name': calendar.get_holiday_detail(date_obj) if is_holiday else None
    }

# @mcp.tool()
# def is_holiday(date: str) -> Dict:
#     """Check if a given date is a holiday in China."""
#     return check_holiday(date)

# class HolidayRequest(BaseModel):
#     date: str

@app.get("/is_holiday")
async def api_is_holiday(date: str):
    """FastAPI endpoint for holiday checking."""
    try:
        return check_holiday(date)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

def get_best_season(climate: str) -> Dict:
    """Get best travel season for a given climate type."""
    if climate not in SEASONAL_RECOMMENDATIONS:
        raise ValueError(f'Invalid climate type. Supported: {list(SEASONAL_RECOMMENDATIONS.keys())}')

    recommendations = SEASONAL_RECOMMENDATIONS[climate]
    return {
        'climate': climate,
        'best_months': recommendations['best'],
        'good_months': recommendations['good'],
        'avoid_months': recommendations['avoid'],
        'recommendation': f"For {climate} climates, the best months are {', '.join(recommendations['best'])}."
    }

# @mcp.tool()
# def best_season(climate: str) -> Dict:
#     """Get recommended travel seasons for different climate types."""
#     return get_best_season(climate)
#
# class SeasonRequest(BaseModel):
#     climate: str

@app.get("/best_season")
async def api_best_season(climate: str):
    """FastAPI endpoint for season recommendations."""
    try:
        return get_best_season(climate)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

REASONABLE_DURATIONS = {
    'city_break': (2, 5),
    'beach': (5, 14),
    'adventure': (7, 21),
    'cultural': (5, 14),
    'road_trip': (3, 21),
    'cruise': (7, 21)
}

def check_trip_duration(start_date: str, end_date: str, destination_type: str) -> Dict:
    """Validate if trip duration is reasonable for destination type."""
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d').date()
        end = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError('Invalid date format. Use YYYY-MM-DD')

    if start > end:
        raise ValueError('Start date must be before end date')

    if destination_type not in REASONABLE_DURATIONS:
        raise ValueError(f'Invalid destination type. Supported: {list(REASONABLE_DURATIONS.keys())}')

    duration_days = (end - start).days + 1
    min_days, max_days = REASONABLE_DURATIONS[destination_type]
    is_reasonable = min_days <= duration_days <= max_days

    if is_reasonable:
        feedback = f"A {duration_days}-day trip is reasonable for a {destination_type.replace('_', ' ')}."
    elif duration_days < min_days:
        feedback = f"A {duration_days}-day trip might be too short for a {destination_type.replace('_', ' ')}. Recommended minimum is {min_days} days."
    else:
        feedback = f"A {duration_days}-day trip might be too long for a {destination_type.replace('_', ' ')}. Recommended maximum is {max_days} days."

    return {
        'start_date': start_date,
        'end_date': end_date,
        'duration_days': duration_days,
        'destination_type': destination_type,
        'is_reasonable': is_reasonable,
        'recommended_min_days': min_days,
        'recommended_max_days': max_days,
        'feedback': feedback
    }

# @mcp.tool()
# def check_duration(start_date: str, end_date: str, destination_type: str) -> Dict:
#     """Check if travel duration is reasonable for destination type."""
#     return check_trip_duration(start_date, end_date, destination_type)
#
# class DurationRequest(BaseModel):
#     start_date: str
#     end_date: str
#     destination_type: str

@app.get("/check_duration")
async def api_check_duration(start_date: str, end_date: str, destination_type: str):
    """FastAPI endpoint for duration validation."""
    try:
        return check_trip_duration(start_date, end_date, destination_type)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


def run_server(port: int = 8012) -> None:
    """Run the FastAPI server."""
    import uvicorn
    logger.info(f'Starting travel_time_server on port {port}...')
    uvicorn.run(app, host="0.0.0.0", port=port)

if __name__ == '__main__':
    run_server()
