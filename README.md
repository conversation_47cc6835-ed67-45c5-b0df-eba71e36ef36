# Multi-Agents 出行规划

一个基于多智能体的旅行规划系统，包括订酒店、订票、行程规划、订单查询四大智能体。

## 快速开始

### 创建 Python 环境

```bash
conda create --name adk python=3.12
conda activate adk
```

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置代理（可选）

若adk使用google gemini系列模型，需要配置代理 

```bash
export http_proxy=*************:7890
export https_proxy=*************:7890
```

### 启动 ADK Server

```bash
cd multi_agents
# 前台调试
adk api_server
# 后台运行
nohup adk api_server > adk.log 2>&1 &
```

### 启动 Travel Planner

```bash
cd travel_planner
nohup python -u app.py > app.log 2>&1 &
```

### 查看日志

```bash
tail -f multi_agents/adk.log
# 或
tail -f multi_agents/travel_planner/app.log
```

---

## 一键启动脚本

赋予执行权限并运行：

```bash
chmod +x start.sh
# 使用默认端口 adk-port: 8000 app_port: 11513
./start.sh

# 指定ADK端口
./start.sh --adk-port 8001

# 指定两个端口
./start.sh --adk-port 8000 --app-port 11513
```

---

## 一键停止脚本

赋予执行权限并运行：

```bash
chmod +x stop.sh
./stop.sh
```
