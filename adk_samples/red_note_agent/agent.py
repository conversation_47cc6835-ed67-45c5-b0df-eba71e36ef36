from contextlib import AsyncExitStack
from google.adk.agents.llm_agent import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, SseServerParams
import os

"""
六一期间带孩子去千岛湖玩，两大一小，玩三天。请在小红书上查找千岛湖的旅游景区，并给出一个旅游攻略。
六一期间带孩子去千岛湖玩，两大一小，玩三天。请在携程上查找千岛湖的旅游景区，并给出一个旅游攻略。
"""

xhs_instruction = """
你是专业的小红书AI助手，能够调用各种工具帮助用户完成工作。在执行任务时，你需要遵守以下规则：

1. **数据安全验证**：`xsec_token` 是验证数据安全性的关键。每次调用工具时（如果需要），你必须携带正确的 `xsec_token` 值，并严格保证数据的对应性和准确性。

2. **安全风控处理**：如果连续获取数据失败，可能是触发了网站的安全风控机制。在这种情况下，你需要提醒用户打开MCP助手检查账号情况。在用户确认账号无异常后，你才能继续完成任务。

3. **数据格式选择**：工具返回的结果优先以 **CSV 格式** 提供（整理后的数据），其次是 **JSON 格式**（原始数据）。你需要根据用户的需求，从结果中提取并提供正确的数据格式。

你将以中文与用户进行交流，确保沟通顺畅，高效完成任务。
"""

async def create_agent():
    """Gets tools from MCP Server."""
    common_exit_stack = AsyncExitStack()
    tools, exit_stack = await MCPToolset.from_server(
      connection_params=SseServerParams(
          url="http://localhost:9999/mcp"
      ),
      async_exit_stack=common_exit_stack
    )
    print(tools)

    agent = LlmAgent(
        model='gemini-2.0-flash',
        name='小红书助手',
        instruction=xhs_instruction,
        tools=tools,
    )
    return agent, exit_stack


root_agent = create_agent()

# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(create_agent())
