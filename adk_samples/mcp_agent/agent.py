from google.adk.agents.llm_agent import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
import os


async def create_agent():
    # print(f'当前工作目录：{os.getcwd()}')
    # print("\n当前环境变量:")
    # for key, value in os.environ.items():
    #     if not key.startswith('_'):  # 过滤系统变量
    #         print(f"{key}={value}")
    # print('before create agent')

    """Gets tools from MCP Server."""
    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='cmd',
            args=[
                "/c",
                "npx",
                "-y",
                "@modelcontextprotocol/server-filesystem",
                # TODO: IMPORTANT! Change the path below to an ABSOLUTE path on your system.
                "E:/temp/4",
            ],
            # command='cmd',
            # args=[
            #     "/c",
            #     "npx",
            #     "-y",
            #     "@modelcontextprotocol/server-filesystem",
            #     # TODO: IMPORTANT! Change the path below to an ABSOLUTE path on your system.
            #     "E:\\temp\\4",
            # ],
            # command='npx',
            # args=[
            #     "-y",
            #     "@amap/amap-maps-mcp-server",
            # ],
        )
    )
    # print('after create agent')
    print(tools)

    agent = LlmAgent(
        model='gemini-2.0-flash',
        name='enterprise_assistant',
        instruction=(
            'Help user accessing their file systems'
        ),
        tools=tools,
    )
    return agent, exit_stack


root_agent = create_agent()

# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(create_agent())
