from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

server_params = StdioServerParameters(
    command="npx.cmd",
    args=[
        "-y",
        "@modelcontextprotocol/server-filesystem",
        ".",
    ],
    env=None,
)

async def run():
    # connecting
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # initialization
            await session.initialize()

            # list tools
            tools = await session.list_tools()
            print("Tools:", tools)

            # call tools
            indices = await session.call_tool("list_allowed_directories")
            print("Indices:", indices)

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())