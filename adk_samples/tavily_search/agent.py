from google.adk.agents.llm_agent import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
import os

"""
六一期间带孩子去千岛湖玩，两大一小，玩三天。请在小红书上查找千岛湖的旅游景区，并给出一个旅游攻略。
六一期间带孩子去千岛湖玩，两大一小，玩三天。请在携程上查找千岛湖的旅游景区，并给出一个旅游攻略。
"""


async def create_agent():
    """Gets tools from MCP Server."""
    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='cmd',
            args=[
                "/c",
                "npx",
                "-y",
                "tavily-mcp@0.1.4",
            ],
            env={
                "TAVILY_API_KEY": "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"
            },
        )
    )
    print(tools)

    agent = LlmAgent(
        model='gemini-2.0-flash',
        name='数据搜索',
        instruction=(
            '帮助用户快速获取结构化搜索结果，构建知识库'
        ),
        tools=tools,
    )
    return agent, exit_stack


root_agent = create_agent()

# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(create_agent())
