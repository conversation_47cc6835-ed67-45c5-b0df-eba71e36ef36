from google.adk.agents.llm_agent import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
import os


async def create_agent():
    """Gets tools from MCP Server."""
    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='uvx',
            args=[
                "mcp-server-time",
                "--local-timezone=Asia/Shanghai",
            ],
        )
    )
    print(tools)

    agent = LlmAgent(
        model='gemini-2.0-flash',
        name='时间助手',
        instruction=(
            '帮助用户查询时间和时区等'
        ),
        tools=tools,
    )
    return agent, exit_stack


root_agent = create_agent()

# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(create_agent())
