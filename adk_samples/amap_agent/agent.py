from google.adk.agents.llm_agent import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
import os


async def create_agent():
    # print(f'当前工作目录：{os.getcwd()}')
    # print("\n当前环境变量:")
    # for key, value in os.environ.items():
    #     if not key.startswith('_'):  # 过滤系统变量
    #         print(f"{key}={value}")
    # print('before create agent')

    """Gets tools from MCP Server."""
    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='cmd',
            args=[
                "/c",
                "npx",
                "-y",
                "@amap/amap-maps-mcp-server",
            ],
            env={
                "AMAP_MAPS_API_KEY": "e700e4c44011116037fd96e3d78c9822"
            },
        )
    )
    # print('after create agent')
    print(tools)

    agent = LlmAgent(
        model='gemini-2.0-flash',
        name='高德地图出行助手',
        instruction=(
            '帮助用户查询POI信息、规划路线、查询天气等'
        ),
        tools=tools,
    )
    return agent, exit_stack


root_agent = create_agent()

# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(create_agent())
