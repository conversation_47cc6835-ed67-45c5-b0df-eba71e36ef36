"""
环境变量加载工具：在各级目录中使用这个模块来加载公共的环境变量
"""

import os
from pathlib import Path
from dotenv import load_dotenv


def load_env_hierarchical():
    """
    加载环境变量文件，优先查找当前目录的 .env，然后向上层目录递归查找
    返回找到的第一个 .env 文件的路径，如果没找到则返回 None
    """
    current_dir = Path(os.path.dirname(os.path.abspath(__file__)))

    # 查找包含当前目录在内的所有父目录
    while True:
        env_path = current_dir / '.env'
        if env_path.exists():
            # 找到 .env 文件，加载它
            load_dotenv(dotenv_path=env_path)
            return env_path

        # 如果到达了文件系统的根目录，停止查找
        if current_dir == current_dir.parent:
            break

        # 继续向上级目录查找
        current_dir = current_dir.parent

    # 如果没有找到 .env 文件
    return None


# 当这个模块被导入时自动加载环境变量
env_path = load_env_hierarchical()
if env_path:
    print(f"已加载环境变量文件: {env_path}")
else:
    print("警告: 未找到 .env 文件")

# 示例使用
if __name__ == "__main__":
    # 打印找到的环境变量
    import os

    print("\n当前环境变量:")
    for key, value in os.environ.items():
        if not key.startswith('_'):  # 过滤系统变量
            print(f"{key}={value}")